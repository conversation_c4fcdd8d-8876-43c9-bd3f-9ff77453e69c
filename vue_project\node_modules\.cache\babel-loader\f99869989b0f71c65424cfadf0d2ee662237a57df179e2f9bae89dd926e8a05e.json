{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, openBlock as _openBlock, createElementBlock as _createElementBlock, renderList as _renderList, Fragment as _Fragment, resolveComponent as _resolveComponent, createBlock as _createBlock, withCtx as _withCtx, createVNode as _createVNode, createTextVNode as _createTextVNode, resolveDirective as _resolveDirective, withDirectives as _withDirectives } from \"vue\";\nconst _hoisted_1 = {\n  class: \"admin-data-manager\"\n};\nconst _hoisted_2 = {\n  class: \"fixed-header\"\n};\nconst _hoisted_3 = {\n  class: \"page-header\"\n};\nconst _hoisted_4 = {\n  class: \"header-stats\"\n};\nconst _hoisted_5 = {\n  key: 0\n};\nconst _hoisted_6 = {\n  key: 1,\n  class: \"selected-info\"\n};\nconst _hoisted_7 = {\n  class: \"toolbar-container\"\n};\nconst _hoisted_8 = {\n  class: \"toolbar\"\n};\nconst _hoisted_9 = {\n  class: \"toolbar-left\"\n};\nconst _hoisted_10 = {\n  class: \"toolbar-right\"\n};\nconst _hoisted_11 = {\n  class: \"scrollable-content\"\n};\nconst _hoisted_12 = [\"href\"];\nconst _hoisted_13 = {\n  key: 1,\n  class: \"data-empty\"\n};\nconst _hoisted_14 = [\"onClick\", \"title\"];\nconst _hoisted_15 = {\n  key: 1,\n  class: \"file-empty\"\n};\nconst _hoisted_16 = [\"onClick\", \"title\"];\nconst _hoisted_17 = {\n  key: 1,\n  class: \"file-empty\"\n};\nconst _hoisted_18 = [\"onClick\", \"title\"];\nconst _hoisted_19 = {\n  key: 1,\n  class: \"file-empty\"\n};\nconst _hoisted_20 = [\"onClick\", \"title\"];\nconst _hoisted_21 = {\n  key: 1,\n  class: \"file-empty\"\n};\nconst _hoisted_22 = [\"onClick\", \"title\"];\nconst _hoisted_23 = {\n  key: 1,\n  class: \"file-empty\"\n};\nconst _hoisted_24 = [\"onClick\", \"title\"];\nconst _hoisted_25 = {\n  key: 1,\n  class: \"file-empty\"\n};\nconst _hoisted_26 = [\"onClick\", \"title\"];\nconst _hoisted_27 = {\n  key: 1,\n  class: \"file-empty\"\n};\nconst _hoisted_28 = [\"onClick\", \"title\"];\nconst _hoisted_29 = {\n  key: 1,\n  class: \"file-empty\"\n};\nconst _hoisted_30 = [\"onClick\", \"title\"];\nconst _hoisted_31 = {\n  key: 1,\n  class: \"file-empty\"\n};\nconst _hoisted_32 = [\"onClick\", \"title\"];\nconst _hoisted_33 = {\n  key: 1,\n  class: \"file-empty\"\n};\nconst _hoisted_34 = [\"onClick\", \"title\"];\nconst _hoisted_35 = {\n  key: 1,\n  class: \"file-empty\"\n};\nconst _hoisted_36 = [\"onClick\", \"title\"];\nconst _hoisted_37 = {\n  key: 1,\n  class: \"file-empty\"\n};\nconst _hoisted_38 = [\"onClick\", \"title\"];\nconst _hoisted_39 = {\n  key: 1,\n  class: \"file-empty\"\n};\nconst _hoisted_40 = [\"onClick\", \"title\"];\nconst _hoisted_41 = {\n  key: 1,\n  class: \"file-empty\"\n};\nconst _hoisted_42 = [\"onClick\", \"title\"];\nconst _hoisted_43 = {\n  key: 1,\n  class: \"file-empty\"\n};\nconst _hoisted_44 = {\n  class: \"pagination-container\"\n};\nconst _hoisted_45 = {\n  class: \"file-management-grid\"\n};\nconst _hoisted_46 = {\n  class: \"file-item\"\n};\nconst _hoisted_47 = {\n  class: \"file-controls\"\n};\nconst _hoisted_48 = {\n  key: 0,\n  class: \"current-file\"\n};\nconst _hoisted_49 = {\n  key: 1,\n  class: \"no-file\"\n};\nconst _hoisted_50 = {\n  class: \"file-item\"\n};\nconst _hoisted_51 = {\n  class: \"file-controls\"\n};\nconst _hoisted_52 = {\n  key: 0,\n  class: \"current-file\"\n};\nconst _hoisted_53 = {\n  key: 1,\n  class: \"no-file\"\n};\nconst _hoisted_54 = {\n  class: \"file-item\"\n};\nconst _hoisted_55 = {\n  class: \"file-controls\"\n};\nconst _hoisted_56 = {\n  key: 0,\n  class: \"current-file\"\n};\nconst _hoisted_57 = {\n  key: 1,\n  class: \"no-file\"\n};\nconst _hoisted_58 = {\n  class: \"file-item\"\n};\nconst _hoisted_59 = {\n  class: \"file-controls\"\n};\nconst _hoisted_60 = {\n  key: 0,\n  class: \"current-file\"\n};\nconst _hoisted_61 = {\n  key: 1,\n  class: \"no-file\"\n};\nconst _hoisted_62 = {\n  class: \"file-item\"\n};\nconst _hoisted_63 = {\n  class: \"file-controls\"\n};\nconst _hoisted_64 = {\n  key: 0,\n  class: \"current-file\"\n};\nconst _hoisted_65 = {\n  key: 1,\n  class: \"no-file\"\n};\nconst _hoisted_66 = {\n  class: \"file-item\"\n};\nconst _hoisted_67 = {\n  class: \"file-controls\"\n};\nconst _hoisted_68 = {\n  key: 0,\n  class: \"current-file\"\n};\nconst _hoisted_69 = {\n  key: 1,\n  class: \"no-file\"\n};\nconst _hoisted_70 = {\n  class: \"file-item\"\n};\nconst _hoisted_71 = {\n  class: \"file-controls\"\n};\nconst _hoisted_72 = {\n  key: 0,\n  class: \"current-file\"\n};\nconst _hoisted_73 = {\n  key: 1,\n  class: \"no-file\"\n};\nconst _hoisted_74 = {\n  class: \"file-item\"\n};\nconst _hoisted_75 = {\n  class: \"file-controls\"\n};\nconst _hoisted_76 = {\n  key: 0,\n  class: \"current-file\"\n};\nconst _hoisted_77 = {\n  key: 1,\n  class: \"no-file\"\n};\nconst _hoisted_78 = {\n  class: \"file-item\"\n};\nconst _hoisted_79 = {\n  class: \"file-controls\"\n};\nconst _hoisted_80 = {\n  key: 0,\n  class: \"current-file\"\n};\nconst _hoisted_81 = {\n  key: 1,\n  class: \"no-file\"\n};\nconst _hoisted_82 = {\n  class: \"file-item\"\n};\nconst _hoisted_83 = {\n  class: \"file-controls\"\n};\nconst _hoisted_84 = {\n  key: 0,\n  class: \"current-file\"\n};\nconst _hoisted_85 = {\n  key: 1,\n  class: \"no-file\"\n};\nconst _hoisted_86 = {\n  class: \"file-item\"\n};\nconst _hoisted_87 = {\n  class: \"file-controls\"\n};\nconst _hoisted_88 = {\n  key: 0,\n  class: \"current-file\"\n};\nconst _hoisted_89 = {\n  key: 1,\n  class: \"no-file\"\n};\nconst _hoisted_90 = {\n  class: \"file-item\"\n};\nconst _hoisted_91 = {\n  class: \"file-controls\"\n};\nconst _hoisted_92 = {\n  key: 0,\n  class: \"current-file\"\n};\nconst _hoisted_93 = {\n  key: 1,\n  class: \"no-file\"\n};\nconst _hoisted_94 = {\n  class: \"file-item\"\n};\nconst _hoisted_95 = {\n  class: \"file-controls\"\n};\nconst _hoisted_96 = {\n  key: 0,\n  class: \"current-file\"\n};\nconst _hoisted_97 = {\n  key: 1,\n  class: \"no-file\"\n};\nconst _hoisted_98 = {\n  class: \"file-item\"\n};\nconst _hoisted_99 = {\n  class: \"file-controls\"\n};\nconst _hoisted_100 = {\n  key: 0,\n  class: \"current-file\"\n};\nconst _hoisted_101 = {\n  key: 1,\n  class: \"no-file\"\n};\nconst _hoisted_102 = {\n  class: \"file-item\"\n};\nconst _hoisted_103 = {\n  class: \"file-controls\"\n};\nconst _hoisted_104 = {\n  key: 0,\n  class: \"current-file\"\n};\nconst _hoisted_105 = {\n  key: 1,\n  class: \"no-file\"\n};\nconst _hoisted_106 = {\n  class: \"dialog-footer\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_option = _resolveComponent(\"el-option\");\n  const _component_el_select = _resolveComponent(\"el-select\");\n  const _component_Refresh = _resolveComponent(\"Refresh\");\n  const _component_el_icon = _resolveComponent(\"el-icon\");\n  const _component_el_button = _resolveComponent(\"el-button\");\n  const _component_Delete = _resolveComponent(\"Delete\");\n  const _component_Plus = _resolveComponent(\"Plus\");\n  const _component_el_table_column = _resolveComponent(\"el-table-column\");\n  const _component_el_table = _resolveComponent(\"el-table\");\n  const _component_el_pagination = _resolveComponent(\"el-pagination\");\n  const _component_el_input = _resolveComponent(\"el-input\");\n  const _component_el_form_item = _resolveComponent(\"el-form-item\");\n  const _component_el_input_number = _resolveComponent(\"el-input-number\");\n  const _component_el_divider = _resolveComponent(\"el-divider\");\n  const _component_el_form = _resolveComponent(\"el-form\");\n  const _component_el_dialog = _resolveComponent(\"el-dialog\");\n  const _directive_loading = _resolveDirective(\"loading\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createCommentVNode(\" 固定的标题和工具栏区域 \"), _createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_cache[42] || (_cache[42] = _createElementVNode(\"h2\", null, \"数据表格管理\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_4, [_createElementVNode(\"span\", null, \"总计: \" + _toDisplayString($setup.totalCount) + \" 条记录\", 1 /* TEXT */), $setup.tableData.length > 0 ? (_openBlock(), _createElementBlock(\"span\", _hoisted_5, \" (当前页: \" + _toDisplayString($setup.tableData.length) + \" 条) \", 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true), $setup.selectedRows.length > 0 ? (_openBlock(), _createElementBlock(\"span\", _hoisted_6, \" 已选择: \" + _toDisplayString($setup.selectedRows.length) + \" 条 \", 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true)])]), _createCommentVNode(\" 操作工具栏 \"), _createElementVNode(\"div\", _hoisted_7, [_createElementVNode(\"div\", _hoisted_8, [_createElementVNode(\"div\", _hoisted_9, [_createVNode(_component_el_select, {\n    modelValue: $setup.searchAccession,\n    \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $setup.searchAccession = $event),\n    placeholder: \"请选择Accession\",\n    filterable: \"\",\n    clearable: \"\",\n    style: {\n      \"width\": \"250px\",\n      \"margin-right\": \"10px\"\n    },\n    onChange: $setup.handleAccessionChange,\n    onClear: $setup.handleAccessionClear\n  }, {\n    default: _withCtx(() => [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.allAccessionOptions, accession => {\n      return _openBlock(), _createBlock(_component_el_option, {\n        key: accession,\n        label: accession,\n        value: accession\n      }, null, 8 /* PROPS */, [\"label\", \"value\"]);\n    }), 128 /* KEYED_FRAGMENT */))]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onChange\", \"onClear\"]), _createVNode(_component_el_select, {\n    modelValue: $setup.selectedSubPopulations,\n    \"onUpdate:modelValue\": _cache[1] || (_cache[1] = $event => $setup.selectedSubPopulations = $event),\n    placeholder: \"请选择亚群\",\n    multiple: \"\",\n    \"collapse-tags\": \"\",\n    \"collapse-tags-tooltip\": \"\",\n    clearable: \"\",\n    style: {\n      \"width\": \"300px\",\n      \"margin-right\": \"10px\"\n    },\n    onChange: $setup.handleSubPopulationFilterChange\n  }, {\n    default: _withCtx(() => [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.subPopulationOptions, option => {\n      return _openBlock(), _createBlock(_component_el_option, {\n        key: option.value,\n        label: option.label,\n        value: option.value\n      }, null, 8 /* PROPS */, [\"label\", \"value\"]);\n    }), 128 /* KEYED_FRAGMENT */))]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onChange\"]), _createVNode(_component_el_button, {\n    onClick: $setup.resetFilters\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n      default: _withCtx(() => [_createVNode(_component_Refresh)]),\n      _: 1 /* STABLE */\n    }), _cache[43] || (_cache[43] = _createTextVNode(\" 重置筛选 \"))]),\n    _: 1 /* STABLE */,\n    __: [43]\n  }, 8 /* PROPS */, [\"onClick\"])]), _createElementVNode(\"div\", _hoisted_10, [_createVNode(_component_el_button, {\n    type: \"danger\",\n    disabled: $setup.selectedRows.length === 0,\n    onClick: $setup.handleBatchDelete\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n      default: _withCtx(() => [_createVNode(_component_Delete)]),\n      _: 1 /* STABLE */\n    }), _createTextVNode(\" 批量删除 (\" + _toDisplayString($setup.selectedRows.length) + \") \", 1 /* TEXT */)]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"disabled\", \"onClick\"]), _createVNode(_component_el_button, {\n    type: \"primary\",\n    onClick: _cache[2] || (_cache[2] = $event => $setup.showAddDialog = true)\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n      default: _withCtx(() => [_createVNode(_component_Plus)]),\n      _: 1 /* STABLE */\n    }), _cache[44] || (_cache[44] = _createTextVNode(\" 新增 Accession \"))]),\n    _: 1 /* STABLE */,\n    __: [44]\n  }), _createVNode(_component_el_button, {\n    type: \"success\",\n    onClick: $setup.handleUpdateData\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n      default: _withCtx(() => [_createVNode(_component_Refresh)]),\n      _: 1 /* STABLE */\n    }), _cache[45] || (_cache[45] = _createTextVNode(\" 更新数据 \"))]),\n    _: 1 /* STABLE */,\n    __: [45]\n  }, 8 /* PROPS */, [\"onClick\"])])])])]), _createCommentVNode(\" 可滚动的内容区域 \"), _createElementVNode(\"div\", _hoisted_11, [_createCommentVNode(\" 数据表格 \"), _withDirectives((_openBlock(), _createBlock(_component_el_table, {\n    data: $setup.tableData,\n    border: \"\",\n    stripe: \"\",\n    style: {\n      \"width\": \"100%\"\n    },\n    \"header-cell-style\": {\n      background: '#f0f5ff',\n      color: '#1a56db',\n      fontWeight: 'bold'\n    },\n    onSelectionChange: $setup.handleSelectionChange\n  }, {\n    default: _withCtx(() => [_createCommentVNode(\" 多选框列 \"), _createVNode(_component_el_table_column, {\n      type: \"selection\",\n      width: \"55\",\n      fixed: \"left\"\n    }), _createVNode(_component_el_table_column, {\n      prop: \"accession\",\n      label: \"Accession\",\n      width: \"150\",\n      fixed: \"left\"\n    }), _createVNode(_component_el_table_column, {\n      prop: \"subPopulation\",\n      label: \"SubPopulation\",\n      width: \"120\"\n    }), _createVNode(_component_el_table_column, {\n      prop: \"seqData\",\n      label: \"SeqData\",\n      width: \"200\"\n    }, {\n      default: _withCtx(scope => [scope.row.seqData && scope.row.seqData !== '-' ? (_openBlock(), _createElementBlock(\"a\", {\n        key: 0,\n        href: scope.row.seqData,\n        target: \"_blank\",\n        class: \"data-link\"\n      }, _toDisplayString(scope.row.seqData), 9 /* TEXT, PROPS */, _hoisted_12)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_13, \"-\"))]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_table_column, {\n      prop: \"longitude\",\n      label: \"Longitude\",\n      width: \"100\"\n    }), _createVNode(_component_el_table_column, {\n      prop: \"latitude\",\n      label: \"Latitude\",\n      width: \"100\"\n    }), _createCommentVNode(\" 文件显示列 \"), _createVNode(_component_el_table_column, {\n      label: \"Genome\",\n      width: \"150\"\n    }, {\n      default: _withCtx(scope => [scope.row.genomeFile ? (_openBlock(), _createElementBlock(\"span\", {\n        key: 0,\n        class: \"file-link\",\n        onClick: $event => $setup.downloadFile(scope.row.accession, 'genome'),\n        title: scope.row.genomeFile\n      }, _toDisplayString(scope.row.genomeFile), 9 /* TEXT, PROPS */, _hoisted_14)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_15, \"-\"))]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_table_column, {\n      label: \"Annotation\",\n      width: \"150\"\n    }, {\n      default: _withCtx(scope => [scope.row.annotationFile ? (_openBlock(), _createElementBlock(\"span\", {\n        key: 0,\n        class: \"file-link\",\n        onClick: $event => $setup.downloadFile(scope.row.accession, 'annotation'),\n        title: scope.row.annotationFile\n      }, _toDisplayString(scope.row.annotationFile), 9 /* TEXT, PROPS */, _hoisted_16)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_17, \"-\"))]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_table_column, {\n      label: \"Transcriptome.all\",\n      width: \"150\"\n    }, {\n      default: _withCtx(scope => [scope.row.transcriptomeAllFile ? (_openBlock(), _createElementBlock(\"span\", {\n        key: 0,\n        class: \"file-link\",\n        onClick: $event => $setup.downloadFile(scope.row.accession, 'transcriptome.all'),\n        title: scope.row.transcriptomeAllFile\n      }, _toDisplayString(scope.row.transcriptomeAllFile), 9 /* TEXT, PROPS */, _hoisted_18)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_19, \"-\"))]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_table_column, {\n      label: \"Transcriptome.leaf\",\n      width: \"150\"\n    }, {\n      default: _withCtx(scope => [scope.row.transcriptomeLeafFile ? (_openBlock(), _createElementBlock(\"span\", {\n        key: 0,\n        class: \"file-link\",\n        onClick: $event => $setup.downloadFile(scope.row.accession, 'transcriptome.leaf'),\n        title: scope.row.transcriptomeLeafFile\n      }, _toDisplayString(scope.row.transcriptomeLeafFile), 9 /* TEXT, PROPS */, _hoisted_20)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_21, \"-\"))]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_table_column, {\n      label: \"Transcriptome.panicles\",\n      width: \"150\"\n    }, {\n      default: _withCtx(scope => [scope.row.transcriptomePaniclesFile ? (_openBlock(), _createElementBlock(\"span\", {\n        key: 0,\n        class: \"file-link\",\n        onClick: $event => $setup.downloadFile(scope.row.accession, 'transcriptome.panicles'),\n        title: scope.row.transcriptomePaniclesFile\n      }, _toDisplayString(scope.row.transcriptomePaniclesFile), 9 /* TEXT, PROPS */, _hoisted_22)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_23, \"-\"))]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_table_column, {\n      label: \"Transcriptome.shoot\",\n      width: \"150\"\n    }, {\n      default: _withCtx(scope => [scope.row.transcriptomeShootFile ? (_openBlock(), _createElementBlock(\"span\", {\n        key: 0,\n        class: \"file-link\",\n        onClick: $event => $setup.downloadFile(scope.row.accession, 'transcriptome.shoot'),\n        title: scope.row.transcriptomeShootFile\n      }, _toDisplayString(scope.row.transcriptomeShootFile), 9 /* TEXT, PROPS */, _hoisted_24)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_25, \"-\"))]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_table_column, {\n      label: \"Transcriptome.stem\",\n      width: \"150\"\n    }, {\n      default: _withCtx(scope => [scope.row.transcriptomeStemFile ? (_openBlock(), _createElementBlock(\"span\", {\n        key: 0,\n        class: \"file-link\",\n        onClick: $event => $setup.downloadFile(scope.row.accession, 'transcriptome.stem'),\n        title: scope.row.transcriptomeStemFile\n      }, _toDisplayString(scope.row.transcriptomeStemFile), 9 /* TEXT, PROPS */, _hoisted_26)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_27, \"-\"))]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_table_column, {\n      label: \"Transcriptome.root\",\n      width: \"150\"\n    }, {\n      default: _withCtx(scope => [scope.row.transcriptomeRootFile ? (_openBlock(), _createElementBlock(\"span\", {\n        key: 0,\n        class: \"file-link\",\n        onClick: $event => $setup.downloadFile(scope.row.accession, 'transcriptome.root'),\n        title: scope.row.transcriptomeRootFile\n      }, _toDisplayString(scope.row.transcriptomeRootFile), 9 /* TEXT, PROPS */, _hoisted_28)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_29, \"-\"))]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_table_column, {\n      label: \"Codon\",\n      width: \"150\"\n    }, {\n      default: _withCtx(scope => [scope.row.codonFile ? (_openBlock(), _createElementBlock(\"span\", {\n        key: 0,\n        class: \"file-link\",\n        onClick: $event => $setup.downloadFile(scope.row.accession, 'codon'),\n        title: scope.row.codonFile\n      }, _toDisplayString(scope.row.codonFile), 9 /* TEXT, PROPS */, _hoisted_30)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_31, \"-\"))]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_table_column, {\n      label: \"Centromere\",\n      width: \"150\"\n    }, {\n      default: _withCtx(scope => [scope.row.centromereFile ? (_openBlock(), _createElementBlock(\"span\", {\n        key: 0,\n        class: \"file-link\",\n        onClick: $event => $setup.downloadFile(scope.row.accession, 'centromere'),\n        title: scope.row.centromereFile\n      }, _toDisplayString(scope.row.centromereFile), 9 /* TEXT, PROPS */, _hoisted_32)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_33, \"-\"))]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_table_column, {\n      label: \"TEs\",\n      width: \"150\"\n    }, {\n      default: _withCtx(scope => [scope.row.tesFile ? (_openBlock(), _createElementBlock(\"span\", {\n        key: 0,\n        class: \"file-link\",\n        onClick: $event => $setup.downloadFile(scope.row.accession, 'TEs'),\n        title: scope.row.tesFile\n      }, _toDisplayString(scope.row.tesFile), 9 /* TEXT, PROPS */, _hoisted_34)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_35, \"-\"))]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_table_column, {\n      label: \"CoreBlocks\",\n      width: \"150\"\n    }, {\n      default: _withCtx(scope => [scope.row.coreBlocksFile ? (_openBlock(), _createElementBlock(\"span\", {\n        key: 0,\n        class: \"file-link\",\n        onClick: $event => $setup.downloadFile(scope.row.accession, 'coreBlocks'),\n        title: scope.row.coreBlocksFile\n      }, _toDisplayString(scope.row.coreBlocksFile), 9 /* TEXT, PROPS */, _hoisted_36)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_37, \"-\"))]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_table_column, {\n      label: \"miRNA\",\n      width: \"150\"\n    }, {\n      default: _withCtx(scope => [scope.row.miRNAFile ? (_openBlock(), _createElementBlock(\"span\", {\n        key: 0,\n        class: \"file-link\",\n        onClick: $event => $setup.downloadFile(scope.row.accession, 'miRNA'),\n        title: scope.row.miRNAFile\n      }, _toDisplayString(scope.row.miRNAFile), 9 /* TEXT, PROPS */, _hoisted_38)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_39, \"-\"))]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_table_column, {\n      label: \"tRNA\",\n      width: \"150\"\n    }, {\n      default: _withCtx(scope => [scope.row.tRNAFile ? (_openBlock(), _createElementBlock(\"span\", {\n        key: 0,\n        class: \"file-link\",\n        onClick: $event => $setup.downloadFile(scope.row.accession, 'tRNA'),\n        title: scope.row.tRNAFile\n      }, _toDisplayString(scope.row.tRNAFile), 9 /* TEXT, PROPS */, _hoisted_40)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_41, \"-\"))]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_table_column, {\n      label: \"rRNA\",\n      width: \"150\"\n    }, {\n      default: _withCtx(scope => [scope.row.rRNAFile ? (_openBlock(), _createElementBlock(\"span\", {\n        key: 0,\n        class: \"file-link\",\n        onClick: $event => $setup.downloadFile(scope.row.accession, 'rRNA'),\n        title: scope.row.rRNAFile\n      }, _toDisplayString(scope.row.rRNAFile), 9 /* TEXT, PROPS */, _hoisted_42)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_43, \"-\"))]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_table_column, {\n      label: \"操作\",\n      width: \"150\",\n      fixed: \"right\"\n    }, {\n      default: _withCtx(scope => [_createVNode(_component_el_button, {\n        type: \"primary\",\n        size: \"small\",\n        onClick: $event => $setup.editRow(scope.row)\n      }, {\n        default: _withCtx(() => _cache[46] || (_cache[46] = [_createTextVNode(\"编辑\")])),\n        _: 2 /* DYNAMIC */,\n        __: [46]\n      }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"]), _createVNode(_component_el_button, {\n        type: \"danger\",\n        size: \"small\",\n        onClick: $event => $setup.deleteRow(scope.row)\n      }, {\n        default: _withCtx(() => _cache[47] || (_cache[47] = [_createTextVNode(\"删除\")])),\n        _: 2 /* DYNAMIC */,\n        __: [47]\n      }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"])]),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"data\", \"onSelectionChange\"])), [[_directive_loading, $setup.loading]]), _createCommentVNode(\" 分页 \"), _createElementVNode(\"div\", _hoisted_44, [_createVNode(_component_el_pagination, {\n    \"current-page\": $setup.currentPage,\n    \"onUpdate:currentPage\": _cache[3] || (_cache[3] = $event => $setup.currentPage = $event),\n    \"page-size\": $setup.pageSize,\n    \"onUpdate:pageSize\": _cache[4] || (_cache[4] = $event => $setup.pageSize = $event),\n    \"page-sizes\": [20, 50, 100],\n    total: $setup.totalCount,\n    layout: \"total, sizes, prev, pager, next, jumper\",\n    onSizeChange: $setup.handleSizeChange,\n    onCurrentChange: $setup.handleCurrentChange\n  }, null, 8 /* PROPS */, [\"current-page\", \"page-size\", \"total\", \"onSizeChange\", \"onCurrentChange\"])]), _createCommentVNode(\" 新增/编辑对话框 \"), _createVNode(_component_el_dialog, {\n    modelValue: $setup.showAddDialog,\n    \"onUpdate:modelValue\": _cache[41] || (_cache[41] = $event => $setup.showAddDialog = $event),\n    title: $setup.editingRow ? '编辑 Accession' : '新增 Accession',\n    width: \"600px\",\n    onClose: $setup.resetForm\n  }, {\n    footer: _withCtx(() => [_createElementVNode(\"span\", _hoisted_106, [_createVNode(_component_el_button, {\n      onClick: _cache[40] || (_cache[40] = $event => $setup.showAddDialog = false)\n    }, {\n      default: _withCtx(() => _cache[94] || (_cache[94] = [_createTextVNode(\"取消\")])),\n      _: 1 /* STABLE */,\n      __: [94]\n    }), _createVNode(_component_el_button, {\n      type: \"primary\",\n      onClick: $setup.saveData,\n      loading: $setup.saving\n    }, {\n      default: _withCtx(() => _cache[95] || (_cache[95] = [_createTextVNode(\"保存\")])),\n      _: 1 /* STABLE */,\n      __: [95]\n    }, 8 /* PROPS */, [\"onClick\", \"loading\"])])]),\n    default: _withCtx(() => [_createVNode(_component_el_form, {\n      model: $setup.formData,\n      rules: $setup.formRules,\n      ref: \"formRef\",\n      \"label-width\": \"120px\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_form_item, {\n        label: \"Accession\",\n        prop: \"accession\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.formData.accession,\n          \"onUpdate:modelValue\": _cache[5] || (_cache[5] = $event => $setup.formData.accession = $event),\n          disabled: $setup.editingRow\n        }, null, 8 /* PROPS */, [\"modelValue\", \"disabled\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"SubPopulation\",\n        prop: \"subPopulation\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_select, {\n          modelValue: $setup.formData.subPopulation,\n          \"onUpdate:modelValue\": _cache[6] || (_cache[6] = $event => $setup.formData.subPopulation = $event),\n          placeholder: \"请选择或输入亚群\",\n          filterable: \"\",\n          \"allow-create\": \"\",\n          \"default-first-option\": \"\",\n          \"reserve-keyword\": false,\n          onChange: _ctx.handleSubPopulationChange\n        }, {\n          default: _withCtx(() => [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.subPopulationOptions, option => {\n            return _openBlock(), _createBlock(_component_el_option, {\n              key: option.value,\n              label: option.label,\n              value: option.value\n            }, null, 8 /* PROPS */, [\"label\", \"value\"]);\n          }), 128 /* KEYED_FRAGMENT */))]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"modelValue\", \"onChange\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"SeqData URL\",\n        prop: \"seqData\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.formData.seqData,\n          \"onUpdate:modelValue\": _cache[7] || (_cache[7] = $event => $setup.formData.seqData = $event),\n          placeholder: \"请输入SeqData链接，留空则为 -\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"经度\",\n        prop: \"longitude\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input_number, {\n          modelValue: $setup.formData.longitude,\n          \"onUpdate:modelValue\": _cache[8] || (_cache[8] = $event => $setup.formData.longitude = $event),\n          precision: 2,\n          placeholder: \"经度\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"纬度\",\n        prop: \"latitude\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input_number, {\n          modelValue: $setup.formData.latitude,\n          \"onUpdate:modelValue\": _cache[9] || (_cache[9] = $event => $setup.formData.latitude = $event),\n          precision: 2,\n          placeholder: \"纬度\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createCommentVNode(\" 文件管理部分 \"), _createVNode(_component_el_divider, {\n        \"content-position\": \"left\"\n      }, {\n        default: _withCtx(() => _cache[48] || (_cache[48] = [_createTextVNode(\"文件管理\")])),\n        _: 1 /* STABLE */,\n        __: [48]\n      }), _createElementVNode(\"div\", _hoisted_45, [_createCommentVNode(\" Genome \"), _createElementVNode(\"div\", _hoisted_46, [_cache[51] || (_cache[51] = _createElementVNode(\"label\", null, \"Genome:\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_47, [$setup.formData.files.genome ? (_openBlock(), _createElementBlock(\"span\", _hoisted_48, _toDisplayString($setup.formData.files.genome), 1 /* TEXT */)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_49, \"无文件\")), _createVNode(_component_el_button, {\n        size: \"small\",\n        onClick: _cache[10] || (_cache[10] = $event => $setup.selectFile('genome'))\n      }, {\n        default: _withCtx(() => _cache[49] || (_cache[49] = [_createTextVNode(\"选择文件\")])),\n        _: 1 /* STABLE */,\n        __: [49]\n      }), $setup.formData.files.genome ? (_openBlock(), _createBlock(_component_el_button, {\n        key: 2,\n        size: \"small\",\n        type: \"danger\",\n        onClick: _cache[11] || (_cache[11] = $event => $setup.removeFile('genome'))\n      }, {\n        default: _withCtx(() => _cache[50] || (_cache[50] = [_createTextVNode(\"删除\")])),\n        _: 1 /* STABLE */,\n        __: [50]\n      })) : _createCommentVNode(\"v-if\", true)])]), _createCommentVNode(\" Annotation \"), _createElementVNode(\"div\", _hoisted_50, [_cache[54] || (_cache[54] = _createElementVNode(\"label\", null, \"Annotation:\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_51, [$setup.formData.files.annotation ? (_openBlock(), _createElementBlock(\"span\", _hoisted_52, _toDisplayString($setup.formData.files.annotation), 1 /* TEXT */)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_53, \"无文件\")), _createVNode(_component_el_button, {\n        size: \"small\",\n        onClick: _cache[12] || (_cache[12] = $event => $setup.selectFile('annotation'))\n      }, {\n        default: _withCtx(() => _cache[52] || (_cache[52] = [_createTextVNode(\"选择文件\")])),\n        _: 1 /* STABLE */,\n        __: [52]\n      }), $setup.formData.files.annotation ? (_openBlock(), _createBlock(_component_el_button, {\n        key: 2,\n        size: \"small\",\n        type: \"danger\",\n        onClick: _cache[13] || (_cache[13] = $event => $setup.removeFile('annotation'))\n      }, {\n        default: _withCtx(() => _cache[53] || (_cache[53] = [_createTextVNode(\"删除\")])),\n        _: 1 /* STABLE */,\n        __: [53]\n      })) : _createCommentVNode(\"v-if\", true)])]), _createCommentVNode(\" Transcriptome.all \"), _createElementVNode(\"div\", _hoisted_54, [_cache[57] || (_cache[57] = _createElementVNode(\"label\", null, \"Transcriptome.all:\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_55, [$setup.formData.files.transcriptomeAll ? (_openBlock(), _createElementBlock(\"span\", _hoisted_56, _toDisplayString($setup.formData.files.transcriptomeAll), 1 /* TEXT */)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_57, \"无文件\")), _createVNode(_component_el_button, {\n        size: \"small\",\n        onClick: _cache[14] || (_cache[14] = $event => $setup.selectFile('transcriptomeAll'))\n      }, {\n        default: _withCtx(() => _cache[55] || (_cache[55] = [_createTextVNode(\"选择文件\")])),\n        _: 1 /* STABLE */,\n        __: [55]\n      }), $setup.formData.files.transcriptomeAll ? (_openBlock(), _createBlock(_component_el_button, {\n        key: 2,\n        size: \"small\",\n        type: \"danger\",\n        onClick: _cache[15] || (_cache[15] = $event => $setup.removeFile('transcriptomeAll'))\n      }, {\n        default: _withCtx(() => _cache[56] || (_cache[56] = [_createTextVNode(\"删除\")])),\n        _: 1 /* STABLE */,\n        __: [56]\n      })) : _createCommentVNode(\"v-if\", true)])]), _createCommentVNode(\" Transcriptome.leaf \"), _createElementVNode(\"div\", _hoisted_58, [_cache[60] || (_cache[60] = _createElementVNode(\"label\", null, \"Transcriptome.leaf:\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_59, [$setup.formData.files.transcriptomeLeaf ? (_openBlock(), _createElementBlock(\"span\", _hoisted_60, _toDisplayString($setup.formData.files.transcriptomeLeaf), 1 /* TEXT */)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_61, \"无文件\")), _createVNode(_component_el_button, {\n        size: \"small\",\n        onClick: _cache[16] || (_cache[16] = $event => $setup.selectFile('transcriptomeLeaf'))\n      }, {\n        default: _withCtx(() => _cache[58] || (_cache[58] = [_createTextVNode(\"选择文件\")])),\n        _: 1 /* STABLE */,\n        __: [58]\n      }), $setup.formData.files.transcriptomeLeaf ? (_openBlock(), _createBlock(_component_el_button, {\n        key: 2,\n        size: \"small\",\n        type: \"danger\",\n        onClick: _cache[17] || (_cache[17] = $event => $setup.removeFile('transcriptomeLeaf'))\n      }, {\n        default: _withCtx(() => _cache[59] || (_cache[59] = [_createTextVNode(\"删除\")])),\n        _: 1 /* STABLE */,\n        __: [59]\n      })) : _createCommentVNode(\"v-if\", true)])]), _createCommentVNode(\" Transcriptome.panicles \"), _createElementVNode(\"div\", _hoisted_62, [_cache[63] || (_cache[63] = _createElementVNode(\"label\", null, \"Transcriptome.panicles:\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_63, [$setup.formData.files.transcriptomePanicles ? (_openBlock(), _createElementBlock(\"span\", _hoisted_64, _toDisplayString($setup.formData.files.transcriptomePanicles), 1 /* TEXT */)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_65, \"无文件\")), _createVNode(_component_el_button, {\n        size: \"small\",\n        onClick: _cache[18] || (_cache[18] = $event => $setup.selectFile('transcriptomePanicles'))\n      }, {\n        default: _withCtx(() => _cache[61] || (_cache[61] = [_createTextVNode(\"选择文件\")])),\n        _: 1 /* STABLE */,\n        __: [61]\n      }), $setup.formData.files.transcriptomePanicles ? (_openBlock(), _createBlock(_component_el_button, {\n        key: 2,\n        size: \"small\",\n        type: \"danger\",\n        onClick: _cache[19] || (_cache[19] = $event => $setup.removeFile('transcriptomePanicles'))\n      }, {\n        default: _withCtx(() => _cache[62] || (_cache[62] = [_createTextVNode(\"删除\")])),\n        _: 1 /* STABLE */,\n        __: [62]\n      })) : _createCommentVNode(\"v-if\", true)])]), _createCommentVNode(\" Transcriptome.shoot \"), _createElementVNode(\"div\", _hoisted_66, [_cache[66] || (_cache[66] = _createElementVNode(\"label\", null, \"Transcriptome.shoot:\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_67, [$setup.formData.files.transcriptomeShoot ? (_openBlock(), _createElementBlock(\"span\", _hoisted_68, _toDisplayString($setup.formData.files.transcriptomeShoot), 1 /* TEXT */)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_69, \"无文件\")), _createVNode(_component_el_button, {\n        size: \"small\",\n        onClick: _cache[20] || (_cache[20] = $event => $setup.selectFile('transcriptomeShoot'))\n      }, {\n        default: _withCtx(() => _cache[64] || (_cache[64] = [_createTextVNode(\"选择文件\")])),\n        _: 1 /* STABLE */,\n        __: [64]\n      }), $setup.formData.files.transcriptomeShoot ? (_openBlock(), _createBlock(_component_el_button, {\n        key: 2,\n        size: \"small\",\n        type: \"danger\",\n        onClick: _cache[21] || (_cache[21] = $event => $setup.removeFile('transcriptomeShoot'))\n      }, {\n        default: _withCtx(() => _cache[65] || (_cache[65] = [_createTextVNode(\"删除\")])),\n        _: 1 /* STABLE */,\n        __: [65]\n      })) : _createCommentVNode(\"v-if\", true)])]), _createCommentVNode(\" Transcriptome.stem \"), _createElementVNode(\"div\", _hoisted_70, [_cache[69] || (_cache[69] = _createElementVNode(\"label\", null, \"Transcriptome.stem:\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_71, [$setup.formData.files.transcriptomeStem ? (_openBlock(), _createElementBlock(\"span\", _hoisted_72, _toDisplayString($setup.formData.files.transcriptomeStem), 1 /* TEXT */)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_73, \"无文件\")), _createVNode(_component_el_button, {\n        size: \"small\",\n        onClick: _cache[22] || (_cache[22] = $event => $setup.selectFile('transcriptomeStem'))\n      }, {\n        default: _withCtx(() => _cache[67] || (_cache[67] = [_createTextVNode(\"选择文件\")])),\n        _: 1 /* STABLE */,\n        __: [67]\n      }), $setup.formData.files.transcriptomeStem ? (_openBlock(), _createBlock(_component_el_button, {\n        key: 2,\n        size: \"small\",\n        type: \"danger\",\n        onClick: _cache[23] || (_cache[23] = $event => $setup.removeFile('transcriptomeStem'))\n      }, {\n        default: _withCtx(() => _cache[68] || (_cache[68] = [_createTextVNode(\"删除\")])),\n        _: 1 /* STABLE */,\n        __: [68]\n      })) : _createCommentVNode(\"v-if\", true)])]), _createCommentVNode(\" Transcriptome.root \"), _createElementVNode(\"div\", _hoisted_74, [_cache[72] || (_cache[72] = _createElementVNode(\"label\", null, \"Transcriptome.root:\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_75, [$setup.formData.files.transcriptomeRoot ? (_openBlock(), _createElementBlock(\"span\", _hoisted_76, _toDisplayString($setup.formData.files.transcriptomeRoot), 1 /* TEXT */)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_77, \"无文件\")), _createVNode(_component_el_button, {\n        size: \"small\",\n        onClick: _cache[24] || (_cache[24] = $event => $setup.selectFile('transcriptomeRoot'))\n      }, {\n        default: _withCtx(() => _cache[70] || (_cache[70] = [_createTextVNode(\"选择文件\")])),\n        _: 1 /* STABLE */,\n        __: [70]\n      }), $setup.formData.files.transcriptomeRoot ? (_openBlock(), _createBlock(_component_el_button, {\n        key: 2,\n        size: \"small\",\n        type: \"danger\",\n        onClick: _cache[25] || (_cache[25] = $event => $setup.removeFile('transcriptomeRoot'))\n      }, {\n        default: _withCtx(() => _cache[71] || (_cache[71] = [_createTextVNode(\"删除\")])),\n        _: 1 /* STABLE */,\n        __: [71]\n      })) : _createCommentVNode(\"v-if\", true)])]), _createCommentVNode(\" Codon \"), _createElementVNode(\"div\", _hoisted_78, [_cache[75] || (_cache[75] = _createElementVNode(\"label\", null, \"Codon:\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_79, [$setup.formData.files.codon ? (_openBlock(), _createElementBlock(\"span\", _hoisted_80, _toDisplayString($setup.formData.files.codon), 1 /* TEXT */)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_81, \"无文件\")), _createVNode(_component_el_button, {\n        size: \"small\",\n        onClick: _cache[26] || (_cache[26] = $event => $setup.selectFile('codon'))\n      }, {\n        default: _withCtx(() => _cache[73] || (_cache[73] = [_createTextVNode(\"选择文件\")])),\n        _: 1 /* STABLE */,\n        __: [73]\n      }), $setup.formData.files.codon ? (_openBlock(), _createBlock(_component_el_button, {\n        key: 2,\n        size: \"small\",\n        type: \"danger\",\n        onClick: _cache[27] || (_cache[27] = $event => $setup.removeFile('codon'))\n      }, {\n        default: _withCtx(() => _cache[74] || (_cache[74] = [_createTextVNode(\"删除\")])),\n        _: 1 /* STABLE */,\n        __: [74]\n      })) : _createCommentVNode(\"v-if\", true)])]), _createCommentVNode(\" Centromere \"), _createElementVNode(\"div\", _hoisted_82, [_cache[78] || (_cache[78] = _createElementVNode(\"label\", null, \"Centromere:\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_83, [$setup.formData.files.centromere ? (_openBlock(), _createElementBlock(\"span\", _hoisted_84, _toDisplayString($setup.formData.files.centromere), 1 /* TEXT */)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_85, \"无文件\")), _createVNode(_component_el_button, {\n        size: \"small\",\n        onClick: _cache[28] || (_cache[28] = $event => $setup.selectFile('centromere'))\n      }, {\n        default: _withCtx(() => _cache[76] || (_cache[76] = [_createTextVNode(\"选择文件\")])),\n        _: 1 /* STABLE */,\n        __: [76]\n      }), $setup.formData.files.centromere ? (_openBlock(), _createBlock(_component_el_button, {\n        key: 2,\n        size: \"small\",\n        type: \"danger\",\n        onClick: _cache[29] || (_cache[29] = $event => $setup.removeFile('centromere'))\n      }, {\n        default: _withCtx(() => _cache[77] || (_cache[77] = [_createTextVNode(\"删除\")])),\n        _: 1 /* STABLE */,\n        __: [77]\n      })) : _createCommentVNode(\"v-if\", true)])]), _createCommentVNode(\" TEs \"), _createElementVNode(\"div\", _hoisted_86, [_cache[81] || (_cache[81] = _createElementVNode(\"label\", null, \"TEs:\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_87, [$setup.formData.files.TEs ? (_openBlock(), _createElementBlock(\"span\", _hoisted_88, _toDisplayString($setup.formData.files.TEs), 1 /* TEXT */)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_89, \"无文件\")), _createVNode(_component_el_button, {\n        size: \"small\",\n        onClick: _cache[30] || (_cache[30] = $event => $setup.selectFile('TEs'))\n      }, {\n        default: _withCtx(() => _cache[79] || (_cache[79] = [_createTextVNode(\"选择文件\")])),\n        _: 1 /* STABLE */,\n        __: [79]\n      }), $setup.formData.files.TEs ? (_openBlock(), _createBlock(_component_el_button, {\n        key: 2,\n        size: \"small\",\n        type: \"danger\",\n        onClick: _cache[31] || (_cache[31] = $event => $setup.removeFile('TEs'))\n      }, {\n        default: _withCtx(() => _cache[80] || (_cache[80] = [_createTextVNode(\"删除\")])),\n        _: 1 /* STABLE */,\n        __: [80]\n      })) : _createCommentVNode(\"v-if\", true)])]), _createCommentVNode(\" CoreBlocks \"), _createElementVNode(\"div\", _hoisted_90, [_cache[84] || (_cache[84] = _createElementVNode(\"label\", null, \"CoreBlocks:\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_91, [$setup.formData.files.coreBlocks ? (_openBlock(), _createElementBlock(\"span\", _hoisted_92, _toDisplayString($setup.formData.files.coreBlocks), 1 /* TEXT */)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_93, \"无文件\")), _createVNode(_component_el_button, {\n        size: \"small\",\n        onClick: _cache[32] || (_cache[32] = $event => $setup.selectFile('coreBlocks'))\n      }, {\n        default: _withCtx(() => _cache[82] || (_cache[82] = [_createTextVNode(\"选择文件\")])),\n        _: 1 /* STABLE */,\n        __: [82]\n      }), $setup.formData.files.coreBlocks ? (_openBlock(), _createBlock(_component_el_button, {\n        key: 2,\n        size: \"small\",\n        type: \"danger\",\n        onClick: _cache[33] || (_cache[33] = $event => $setup.removeFile('coreBlocks'))\n      }, {\n        default: _withCtx(() => _cache[83] || (_cache[83] = [_createTextVNode(\"删除\")])),\n        _: 1 /* STABLE */,\n        __: [83]\n      })) : _createCommentVNode(\"v-if\", true)])]), _createCommentVNode(\" miRNA \"), _createElementVNode(\"div\", _hoisted_94, [_cache[87] || (_cache[87] = _createElementVNode(\"label\", null, \"miRNA:\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_95, [$setup.formData.files.miRNA ? (_openBlock(), _createElementBlock(\"span\", _hoisted_96, _toDisplayString($setup.formData.files.miRNA), 1 /* TEXT */)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_97, \"无文件\")), _createVNode(_component_el_button, {\n        size: \"small\",\n        onClick: _cache[34] || (_cache[34] = $event => $setup.selectFile('miRNA'))\n      }, {\n        default: _withCtx(() => _cache[85] || (_cache[85] = [_createTextVNode(\"选择文件\")])),\n        _: 1 /* STABLE */,\n        __: [85]\n      }), $setup.formData.files.miRNA ? (_openBlock(), _createBlock(_component_el_button, {\n        key: 2,\n        size: \"small\",\n        type: \"danger\",\n        onClick: _cache[35] || (_cache[35] = $event => $setup.removeFile('miRNA'))\n      }, {\n        default: _withCtx(() => _cache[86] || (_cache[86] = [_createTextVNode(\"删除\")])),\n        _: 1 /* STABLE */,\n        __: [86]\n      })) : _createCommentVNode(\"v-if\", true)])]), _createCommentVNode(\" tRNA \"), _createElementVNode(\"div\", _hoisted_98, [_cache[90] || (_cache[90] = _createElementVNode(\"label\", null, \"tRNA:\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_99, [$setup.formData.files.tRNA ? (_openBlock(), _createElementBlock(\"span\", _hoisted_100, _toDisplayString($setup.formData.files.tRNA), 1 /* TEXT */)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_101, \"无文件\")), _createVNode(_component_el_button, {\n        size: \"small\",\n        onClick: _cache[36] || (_cache[36] = $event => $setup.selectFile('tRNA'))\n      }, {\n        default: _withCtx(() => _cache[88] || (_cache[88] = [_createTextVNode(\"选择文件\")])),\n        _: 1 /* STABLE */,\n        __: [88]\n      }), $setup.formData.files.tRNA ? (_openBlock(), _createBlock(_component_el_button, {\n        key: 2,\n        size: \"small\",\n        type: \"danger\",\n        onClick: _cache[37] || (_cache[37] = $event => $setup.removeFile('tRNA'))\n      }, {\n        default: _withCtx(() => _cache[89] || (_cache[89] = [_createTextVNode(\"删除\")])),\n        _: 1 /* STABLE */,\n        __: [89]\n      })) : _createCommentVNode(\"v-if\", true)])]), _createCommentVNode(\" rRNA \"), _createElementVNode(\"div\", _hoisted_102, [_cache[93] || (_cache[93] = _createElementVNode(\"label\", null, \"rRNA:\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_103, [$setup.formData.files.rRNA ? (_openBlock(), _createElementBlock(\"span\", _hoisted_104, _toDisplayString($setup.formData.files.rRNA), 1 /* TEXT */)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_105, \"无文件\")), _createVNode(_component_el_button, {\n        size: \"small\",\n        onClick: _cache[38] || (_cache[38] = $event => $setup.selectFile('rRNA'))\n      }, {\n        default: _withCtx(() => _cache[91] || (_cache[91] = [_createTextVNode(\"选择文件\")])),\n        _: 1 /* STABLE */,\n        __: [91]\n      }), $setup.formData.files.rRNA ? (_openBlock(), _createBlock(_component_el_button, {\n        key: 2,\n        size: \"small\",\n        type: \"danger\",\n        onClick: _cache[39] || (_cache[39] = $event => $setup.removeFile('rRNA'))\n      }, {\n        default: _withCtx(() => _cache[92] || (_cache[92] = [_createTextVNode(\"删除\")])),\n        _: 1 /* STABLE */,\n        __: [92]\n      })) : _createCommentVNode(\"v-if\", true)])])])]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"model\", \"rules\"])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"title\", \"onClose\"])])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createCommentVNode", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_toDisplayString", "$setup", "totalCount", "tableData", "length", "_hoisted_5", "selectedRows", "_hoisted_6", "_hoisted_7", "_hoisted_8", "_hoisted_9", "_createVNode", "_component_el_select", "searchAccession", "$event", "placeholder", "filterable", "clearable", "style", "onChange", "handleAccessionChange", "onClear", "handleAccessionClear", "_Fragment", "_renderList", "allAccessionOptions", "accession", "_createBlock", "_component_el_option", "key", "label", "value", "selectedSubPopulations", "multiple", "handleSubPopulationFilterChange", "subPopulationOptions", "option", "_component_el_button", "onClick", "resetFilters", "_component_el_icon", "_component_Refresh", "_hoisted_10", "type", "disabled", "handleBatchDelete", "_component_Delete", "_cache", "showAddDialog", "_component_Plus", "handleUpdateData", "_hoisted_11", "_component_el_table", "data", "border", "stripe", "background", "color", "fontWeight", "onSelectionChange", "handleSelectionChange", "_component_el_table_column", "width", "fixed", "prop", "default", "_withCtx", "scope", "row", "seqData", "href", "target", "_hoisted_12", "_hoisted_13", "genomeFile", "downloadFile", "title", "_hoisted_14", "_hoisted_15", "annotationFile", "_hoisted_16", "_hoisted_17", "transcriptomeAllFile", "_hoisted_18", "_hoisted_19", "transcriptomeLeafFile", "_hoisted_20", "_hoisted_21", "transcriptomePaniclesFile", "_hoisted_22", "_hoisted_23", "transcriptomeShootFile", "_hoisted_24", "_hoisted_25", "transcriptomeStemFile", "_hoisted_26", "_hoisted_27", "transcriptomeRootFile", "_hoisted_28", "_hoisted_29", "codonFile", "_hoisted_30", "_hoisted_31", "centromereFile", "_hoisted_32", "_hoisted_33", "tesFile", "_hoisted_34", "_hoisted_35", "coreBlocksFile", "_hoisted_36", "_hoisted_37", "miRNAFile", "_hoisted_38", "_hoisted_39", "tRNAFile", "_hoisted_40", "_hoisted_41", "rRNAFile", "_hoisted_42", "_hoisted_43", "size", "editRow", "deleteRow", "loading", "_hoisted_44", "_component_el_pagination", "currentPage", "pageSize", "total", "layout", "onSizeChange", "handleSizeChange", "onCurrentChange", "handleCurrentChange", "_component_el_dialog", "editingRow", "onClose", "resetForm", "footer", "_hoisted_106", "saveData", "saving", "_component_el_form", "model", "formData", "rules", "formRules", "ref", "_component_el_form_item", "_component_el_input", "subPopulation", "_ctx", "handleSubPopulationChange", "_component_el_input_number", "longitude", "precision", "latitude", "_component_el_divider", "_hoisted_45", "_hoisted_46", "_hoisted_47", "files", "genome", "_hoisted_48", "_hoisted_49", "selectFile", "removeFile", "_hoisted_50", "_hoisted_51", "annotation", "_hoisted_52", "_hoisted_53", "_hoisted_54", "_hoisted_55", "transcriptomeAll", "_hoisted_56", "_hoisted_57", "_hoisted_58", "_hoisted_59", "transcriptomeLeaf", "_hoisted_60", "_hoisted_61", "_hoisted_62", "_hoisted_63", "transcriptomePanicles", "_hoisted_64", "_hoisted_65", "_hoisted_66", "_hoisted_67", "transcriptomeShoot", "_hoisted_68", "_hoisted_69", "_hoisted_70", "_hoisted_71", "transcriptomeStem", "_hoisted_72", "_hoisted_73", "_hoisted_74", "_hoisted_75", "transcriptomeRoot", "_hoisted_76", "_hoisted_77", "_hoisted_78", "_hoisted_79", "codon", "_hoisted_80", "_hoisted_81", "_hoisted_82", "_hoisted_83", "centromere", "_hoisted_84", "_hoisted_85", "_hoisted_86", "_hoisted_87", "TEs", "_hoisted_88", "_hoisted_89", "_hoisted_90", "_hoisted_91", "coreBlocks", "_hoisted_92", "_hoisted_93", "_hoisted_94", "_hoisted_95", "miRNA", "_hoisted_96", "_hoisted_97", "_hoisted_98", "_hoisted_99", "tRNA", "_hoisted_100", "_hoisted_101", "_hoisted_102", "_hoisted_103", "rRNA", "_hoisted_104", "_hoisted_105"], "sources": ["D:\\gene_manage_system\\vue_project\\src\\views\\AdminDataManager.vue"], "sourcesContent": ["<template>\n  <div class=\"admin-data-manager\">\n    <!-- 固定的标题和工具栏区域 -->\n    <div class=\"fixed-header\">\n      <div class=\"page-header\">\n        <h2>数据表格管理</h2>\n        <div class=\"header-stats\">\n          <span>总计: {{ totalCount }} 条记录</span>\n          <span v-if=\"tableData.length > 0\">\n            (当前页: {{ tableData.length }} 条)\n          </span>\n          <span v-if=\"selectedRows.length > 0\" class=\"selected-info\">\n            已选择: {{ selectedRows.length }} 条\n          </span>\n        </div>\n      </div>\n\n      <!-- 操作工具栏 -->\n      <div class=\"toolbar-container\">\n      <div class=\"toolbar\">\n        <div class=\"toolbar-left\">\n          <el-select\n            v-model=\"searchAccession\"\n            placeholder=\"请选择Accession\"\n            filterable\n            clearable\n            style=\"width: 250px; margin-right: 10px;\"\n            @change=\"handleAccessionChange\"\n            @clear=\"handleAccessionClear\">\n            <el-option\n              v-for=\"accession in allAccessionOptions\"\n              :key=\"accession\"\n              :label=\"accession\"\n              :value=\"accession\" />\n          </el-select>\n\n          <el-select\n            v-model=\"selectedSubPopulations\"\n            placeholder=\"请选择亚群\"\n            multiple\n            collapse-tags\n            collapse-tags-tooltip\n            clearable\n            style=\"width: 300px; margin-right: 10px;\"\n            @change=\"handleSubPopulationFilterChange\">\n            <el-option\n              v-for=\"option in subPopulationOptions\"\n              :key=\"option.value\"\n              :label=\"option.label\"\n              :value=\"option.value\" />\n          </el-select>\n\n          <el-button @click=\"resetFilters\">\n            <el-icon><Refresh /></el-icon>\n            重置筛选\n          </el-button>\n        </div>\n\n        <div class=\"toolbar-right\">\n          <el-button\n            type=\"danger\"\n            :disabled=\"selectedRows.length === 0\"\n            @click=\"handleBatchDelete\">\n            <el-icon><Delete /></el-icon>\n            批量删除 ({{ selectedRows.length }})\n          </el-button>\n          <el-button type=\"primary\" @click=\"showAddDialog = true\">\n            <el-icon><Plus /></el-icon>\n            新增 Accession\n          </el-button>\n          <el-button type=\"success\" @click=\"handleUpdateData\">\n            <el-icon><Refresh /></el-icon>\n            更新数据\n          </el-button>\n        </div>\n      </div>\n    </div>\n    </div>\n\n    <!-- 可滚动的内容区域 -->\n    <div class=\"scrollable-content\">\n      <!-- 数据表格 -->\n      <el-table\n      :data=\"tableData\"\n      v-loading=\"loading\"\n      border\n      stripe\n      style=\"width: 100%\"\n      :header-cell-style=\"{ background: '#f0f5ff', color: '#1a56db', fontWeight: 'bold' }\"\n      @selection-change=\"handleSelectionChange\">\n\n      <!-- 多选框列 -->\n      <el-table-column type=\"selection\" width=\"55\" fixed=\"left\" />\n\n      <el-table-column prop=\"accession\" label=\"Accession\" width=\"150\" fixed=\"left\" />\n      <el-table-column prop=\"subPopulation\" label=\"SubPopulation\" width=\"120\" />\n      <el-table-column prop=\"seqData\" label=\"SeqData\" width=\"200\">\n        <template #default=\"scope\">\n          <a v-if=\"scope.row.seqData && scope.row.seqData !== '-'\" \n             :href=\"scope.row.seqData\" \n             target=\"_blank\" \n             class=\"data-link\">\n            {{ scope.row.seqData }}\n          </a>\n          <span v-else class=\"data-empty\">-</span>\n        </template>\n      </el-table-column>\n      <el-table-column prop=\"longitude\" label=\"Longitude\" width=\"100\" />\n      <el-table-column prop=\"latitude\" label=\"Latitude\" width=\"100\" />\n\n      <!-- 文件显示列 -->\n      <el-table-column label=\"Genome\" width=\"150\">\n        <template #default=\"scope\">\n          <span v-if=\"scope.row.genomeFile\"\n                class=\"file-link\"\n                @click=\"downloadFile(scope.row.accession, 'genome')\"\n                :title=\"scope.row.genomeFile\">\n            {{ scope.row.genomeFile }}\n          </span>\n          <span v-else class=\"file-empty\">-</span>\n        </template>\n      </el-table-column>\n\n      <el-table-column label=\"Annotation\" width=\"150\">\n        <template #default=\"scope\">\n          <span v-if=\"scope.row.annotationFile\"\n                class=\"file-link\"\n                @click=\"downloadFile(scope.row.accession, 'annotation')\"\n                :title=\"scope.row.annotationFile\">\n            {{ scope.row.annotationFile }}\n          </span>\n          <span v-else class=\"file-empty\">-</span>\n        </template>\n      </el-table-column>\n\n      <el-table-column label=\"Transcriptome.all\" width=\"150\">\n        <template #default=\"scope\">\n          <span v-if=\"scope.row.transcriptomeAllFile\"\n                class=\"file-link\"\n                @click=\"downloadFile(scope.row.accession, 'transcriptome.all')\"\n                :title=\"scope.row.transcriptomeAllFile\">\n            {{ scope.row.transcriptomeAllFile }}\n          </span>\n          <span v-else class=\"file-empty\">-</span>\n        </template>\n      </el-table-column>\n\n      <el-table-column label=\"Transcriptome.leaf\" width=\"150\">\n        <template #default=\"scope\">\n          <span v-if=\"scope.row.transcriptomeLeafFile\"\n                class=\"file-link\"\n                @click=\"downloadFile(scope.row.accession, 'transcriptome.leaf')\"\n                :title=\"scope.row.transcriptomeLeafFile\">\n            {{ scope.row.transcriptomeLeafFile }}\n          </span>\n          <span v-else class=\"file-empty\">-</span>\n        </template>\n      </el-table-column>\n\n      <el-table-column label=\"Transcriptome.panicles\" width=\"150\">\n        <template #default=\"scope\">\n          <span v-if=\"scope.row.transcriptomePaniclesFile\"\n                class=\"file-link\"\n                @click=\"downloadFile(scope.row.accession, 'transcriptome.panicles')\"\n                :title=\"scope.row.transcriptomePaniclesFile\">\n            {{ scope.row.transcriptomePaniclesFile }}\n          </span>\n          <span v-else class=\"file-empty\">-</span>\n        </template>\n      </el-table-column>\n\n      <el-table-column label=\"Transcriptome.shoot\" width=\"150\">\n        <template #default=\"scope\">\n          <span v-if=\"scope.row.transcriptomeShootFile\"\n                class=\"file-link\"\n                @click=\"downloadFile(scope.row.accession, 'transcriptome.shoot')\"\n                :title=\"scope.row.transcriptomeShootFile\">\n            {{ scope.row.transcriptomeShootFile }}\n          </span>\n          <span v-else class=\"file-empty\">-</span>\n        </template>\n      </el-table-column>\n\n      <el-table-column label=\"Transcriptome.stem\" width=\"150\">\n        <template #default=\"scope\">\n          <span v-if=\"scope.row.transcriptomeStemFile\"\n                class=\"file-link\"\n                @click=\"downloadFile(scope.row.accession, 'transcriptome.stem')\"\n                :title=\"scope.row.transcriptomeStemFile\">\n            {{ scope.row.transcriptomeStemFile }}\n          </span>\n          <span v-else class=\"file-empty\">-</span>\n        </template>\n      </el-table-column>\n\n      <el-table-column label=\"Transcriptome.root\" width=\"150\">\n        <template #default=\"scope\">\n          <span v-if=\"scope.row.transcriptomeRootFile\"\n                class=\"file-link\"\n                @click=\"downloadFile(scope.row.accession, 'transcriptome.root')\"\n                :title=\"scope.row.transcriptomeRootFile\">\n            {{ scope.row.transcriptomeRootFile }}\n          </span>\n          <span v-else class=\"file-empty\">-</span>\n        </template>\n      </el-table-column>\n\n      <el-table-column label=\"Codon\" width=\"150\">\n        <template #default=\"scope\">\n          <span v-if=\"scope.row.codonFile\"\n                class=\"file-link\"\n                @click=\"downloadFile(scope.row.accession, 'codon')\"\n                :title=\"scope.row.codonFile\">\n            {{ scope.row.codonFile }}\n          </span>\n          <span v-else class=\"file-empty\">-</span>\n        </template>\n      </el-table-column>\n\n      <el-table-column label=\"Centromere\" width=\"150\">\n        <template #default=\"scope\">\n          <span v-if=\"scope.row.centromereFile\"\n                class=\"file-link\"\n                @click=\"downloadFile(scope.row.accession, 'centromere')\"\n                :title=\"scope.row.centromereFile\">\n            {{ scope.row.centromereFile }}\n          </span>\n          <span v-else class=\"file-empty\">-</span>\n        </template>\n      </el-table-column>\n\n      <el-table-column label=\"TEs\" width=\"150\">\n        <template #default=\"scope\">\n          <span v-if=\"scope.row.tesFile\"\n                class=\"file-link\"\n                @click=\"downloadFile(scope.row.accession, 'TEs')\"\n                :title=\"scope.row.tesFile\">\n            {{ scope.row.tesFile }}\n          </span>\n          <span v-else class=\"file-empty\">-</span>\n        </template>\n      </el-table-column>\n\n      <el-table-column label=\"CoreBlocks\" width=\"150\">\n        <template #default=\"scope\">\n          <span v-if=\"scope.row.coreBlocksFile\"\n                class=\"file-link\"\n                @click=\"downloadFile(scope.row.accession, 'coreBlocks')\"\n                :title=\"scope.row.coreBlocksFile\">\n            {{ scope.row.coreBlocksFile }}\n          </span>\n          <span v-else class=\"file-empty\">-</span>\n        </template>\n      </el-table-column>\n\n      <el-table-column label=\"miRNA\" width=\"150\">\n        <template #default=\"scope\">\n          <span v-if=\"scope.row.miRNAFile\"\n                class=\"file-link\"\n                @click=\"downloadFile(scope.row.accession, 'miRNA')\"\n                :title=\"scope.row.miRNAFile\">\n            {{ scope.row.miRNAFile }}\n          </span>\n          <span v-else class=\"file-empty\">-</span>\n        </template>\n      </el-table-column>\n\n      <el-table-column label=\"tRNA\" width=\"150\">\n        <template #default=\"scope\">\n          <span v-if=\"scope.row.tRNAFile\"\n                class=\"file-link\"\n                @click=\"downloadFile(scope.row.accession, 'tRNA')\"\n                :title=\"scope.row.tRNAFile\">\n            {{ scope.row.tRNAFile }}\n          </span>\n          <span v-else class=\"file-empty\">-</span>\n        </template>\n      </el-table-column>\n\n      <el-table-column label=\"rRNA\" width=\"150\">\n        <template #default=\"scope\">\n          <span v-if=\"scope.row.rRNAFile\"\n                class=\"file-link\"\n                @click=\"downloadFile(scope.row.accession, 'rRNA')\"\n                :title=\"scope.row.rRNAFile\">\n            {{ scope.row.rRNAFile }}\n          </span>\n          <span v-else class=\"file-empty\">-</span>\n        </template>\n      </el-table-column>\n\n      <el-table-column label=\"操作\" width=\"150\" fixed=\"right\">\n        <template #default=\"scope\">\n          <el-button type=\"primary\" size=\"small\" @click=\"editRow(scope.row)\">编辑</el-button>\n          <el-button type=\"danger\" size=\"small\" @click=\"deleteRow(scope.row)\">删除</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <!-- 分页 -->\n    <div class=\"pagination-container\">\n      <el-pagination\n        v-model:current-page=\"currentPage\"\n        v-model:page-size=\"pageSize\"\n        :page-sizes=\"[20, 50, 100]\"\n        :total=\"totalCount\"\n        layout=\"total, sizes, prev, pager, next, jumper\"\n        @size-change=\"handleSizeChange\"\n        @current-change=\"handleCurrentChange\"\n      />\n    </div>\n\n    <!-- 新增/编辑对话框 -->\n    <el-dialog\n      v-model=\"showAddDialog\"\n      :title=\"editingRow ? '编辑 Accession' : '新增 Accession'\"\n      width=\"600px\"\n      @close=\"resetForm\">\n      \n      <el-form :model=\"formData\" :rules=\"formRules\" ref=\"formRef\" label-width=\"120px\">\n        <el-form-item label=\"Accession\" prop=\"accession\">\n          <el-input v-model=\"formData.accession\" :disabled=\"editingRow\" />\n        </el-form-item>\n        \n        <el-form-item label=\"SubPopulation\" prop=\"subPopulation\">\n          <el-select\n            v-model=\"formData.subPopulation\"\n            placeholder=\"请选择或输入亚群\"\n            filterable\n            allow-create\n            default-first-option\n            :reserve-keyword=\"false\"\n            @change=\"handleSubPopulationChange\">\n            <el-option\n              v-for=\"option in subPopulationOptions\"\n              :key=\"option.value\"\n              :label=\"option.label\"\n              :value=\"option.value\" />\n          </el-select>\n        </el-form-item>\n        \n        <el-form-item label=\"SeqData URL\" prop=\"seqData\">\n          <el-input v-model=\"formData.seqData\" placeholder=\"请输入SeqData链接，留空则为 -\" />\n        </el-form-item>\n        \n        <el-form-item label=\"经度\" prop=\"longitude\">\n          <el-input-number v-model=\"formData.longitude\" :precision=\"2\" placeholder=\"经度\" />\n        </el-form-item>\n        \n        <el-form-item label=\"纬度\" prop=\"latitude\">\n          <el-input-number v-model=\"formData.latitude\" :precision=\"2\" placeholder=\"纬度\" />\n        </el-form-item>\n\n        <!-- 文件管理部分 -->\n        <el-divider content-position=\"left\">文件管理</el-divider>\n\n        <div class=\"file-management-grid\">\n          <!-- Genome -->\n          <div class=\"file-item\">\n            <label>Genome:</label>\n            <div class=\"file-controls\">\n              <span v-if=\"formData.files.genome\" class=\"current-file\">{{ formData.files.genome }}</span>\n              <span v-else class=\"no-file\">无文件</span>\n              <el-button size=\"small\" @click=\"selectFile('genome')\">选择文件</el-button>\n              <el-button v-if=\"formData.files.genome\" size=\"small\" type=\"danger\" @click=\"removeFile('genome')\">删除</el-button>\n            </div>\n          </div>\n\n          <!-- Annotation -->\n          <div class=\"file-item\">\n            <label>Annotation:</label>\n            <div class=\"file-controls\">\n              <span v-if=\"formData.files.annotation\" class=\"current-file\">{{ formData.files.annotation }}</span>\n              <span v-else class=\"no-file\">无文件</span>\n              <el-button size=\"small\" @click=\"selectFile('annotation')\">选择文件</el-button>\n              <el-button v-if=\"formData.files.annotation\" size=\"small\" type=\"danger\" @click=\"removeFile('annotation')\">删除</el-button>\n            </div>\n          </div>\n\n          <!-- Transcriptome.all -->\n          <div class=\"file-item\">\n            <label>Transcriptome.all:</label>\n            <div class=\"file-controls\">\n              <span v-if=\"formData.files.transcriptomeAll\" class=\"current-file\">{{ formData.files.transcriptomeAll }}</span>\n              <span v-else class=\"no-file\">无文件</span>\n              <el-button size=\"small\" @click=\"selectFile('transcriptomeAll')\">选择文件</el-button>\n              <el-button v-if=\"formData.files.transcriptomeAll\" size=\"small\" type=\"danger\" @click=\"removeFile('transcriptomeAll')\">删除</el-button>\n            </div>\n          </div>\n\n          <!-- Transcriptome.leaf -->\n          <div class=\"file-item\">\n            <label>Transcriptome.leaf:</label>\n            <div class=\"file-controls\">\n              <span v-if=\"formData.files.transcriptomeLeaf\" class=\"current-file\">{{ formData.files.transcriptomeLeaf }}</span>\n              <span v-else class=\"no-file\">无文件</span>\n              <el-button size=\"small\" @click=\"selectFile('transcriptomeLeaf')\">选择文件</el-button>\n              <el-button v-if=\"formData.files.transcriptomeLeaf\" size=\"small\" type=\"danger\" @click=\"removeFile('transcriptomeLeaf')\">删除</el-button>\n            </div>\n          </div>\n\n          <!-- Transcriptome.panicles -->\n          <div class=\"file-item\">\n            <label>Transcriptome.panicles:</label>\n            <div class=\"file-controls\">\n              <span v-if=\"formData.files.transcriptomePanicles\" class=\"current-file\">{{ formData.files.transcriptomePanicles }}</span>\n              <span v-else class=\"no-file\">无文件</span>\n              <el-button size=\"small\" @click=\"selectFile('transcriptomePanicles')\">选择文件</el-button>\n              <el-button v-if=\"formData.files.transcriptomePanicles\" size=\"small\" type=\"danger\" @click=\"removeFile('transcriptomePanicles')\">删除</el-button>\n            </div>\n          </div>\n\n          <!-- Transcriptome.shoot -->\n          <div class=\"file-item\">\n            <label>Transcriptome.shoot:</label>\n            <div class=\"file-controls\">\n              <span v-if=\"formData.files.transcriptomeShoot\" class=\"current-file\">{{ formData.files.transcriptomeShoot }}</span>\n              <span v-else class=\"no-file\">无文件</span>\n              <el-button size=\"small\" @click=\"selectFile('transcriptomeShoot')\">选择文件</el-button>\n              <el-button v-if=\"formData.files.transcriptomeShoot\" size=\"small\" type=\"danger\" @click=\"removeFile('transcriptomeShoot')\">删除</el-button>\n            </div>\n          </div>\n\n          <!-- Transcriptome.stem -->\n          <div class=\"file-item\">\n            <label>Transcriptome.stem:</label>\n            <div class=\"file-controls\">\n              <span v-if=\"formData.files.transcriptomeStem\" class=\"current-file\">{{ formData.files.transcriptomeStem }}</span>\n              <span v-else class=\"no-file\">无文件</span>\n              <el-button size=\"small\" @click=\"selectFile('transcriptomeStem')\">选择文件</el-button>\n              <el-button v-if=\"formData.files.transcriptomeStem\" size=\"small\" type=\"danger\" @click=\"removeFile('transcriptomeStem')\">删除</el-button>\n            </div>\n          </div>\n\n          <!-- Transcriptome.root -->\n          <div class=\"file-item\">\n            <label>Transcriptome.root:</label>\n            <div class=\"file-controls\">\n              <span v-if=\"formData.files.transcriptomeRoot\" class=\"current-file\">{{ formData.files.transcriptomeRoot }}</span>\n              <span v-else class=\"no-file\">无文件</span>\n              <el-button size=\"small\" @click=\"selectFile('transcriptomeRoot')\">选择文件</el-button>\n              <el-button v-if=\"formData.files.transcriptomeRoot\" size=\"small\" type=\"danger\" @click=\"removeFile('transcriptomeRoot')\">删除</el-button>\n            </div>\n          </div>\n\n          <!-- Codon -->\n          <div class=\"file-item\">\n            <label>Codon:</label>\n            <div class=\"file-controls\">\n              <span v-if=\"formData.files.codon\" class=\"current-file\">{{ formData.files.codon }}</span>\n              <span v-else class=\"no-file\">无文件</span>\n              <el-button size=\"small\" @click=\"selectFile('codon')\">选择文件</el-button>\n              <el-button v-if=\"formData.files.codon\" size=\"small\" type=\"danger\" @click=\"removeFile('codon')\">删除</el-button>\n            </div>\n          </div>\n\n          <!-- Centromere -->\n          <div class=\"file-item\">\n            <label>Centromere:</label>\n            <div class=\"file-controls\">\n              <span v-if=\"formData.files.centromere\" class=\"current-file\">{{ formData.files.centromere }}</span>\n              <span v-else class=\"no-file\">无文件</span>\n              <el-button size=\"small\" @click=\"selectFile('centromere')\">选择文件</el-button>\n              <el-button v-if=\"formData.files.centromere\" size=\"small\" type=\"danger\" @click=\"removeFile('centromere')\">删除</el-button>\n            </div>\n          </div>\n\n          <!-- TEs -->\n          <div class=\"file-item\">\n            <label>TEs:</label>\n            <div class=\"file-controls\">\n              <span v-if=\"formData.files.TEs\" class=\"current-file\">{{ formData.files.TEs }}</span>\n              <span v-else class=\"no-file\">无文件</span>\n              <el-button size=\"small\" @click=\"selectFile('TEs')\">选择文件</el-button>\n              <el-button v-if=\"formData.files.TEs\" size=\"small\" type=\"danger\" @click=\"removeFile('TEs')\">删除</el-button>\n            </div>\n          </div>\n\n          <!-- CoreBlocks -->\n          <div class=\"file-item\">\n            <label>CoreBlocks:</label>\n            <div class=\"file-controls\">\n              <span v-if=\"formData.files.coreBlocks\" class=\"current-file\">{{ formData.files.coreBlocks }}</span>\n              <span v-else class=\"no-file\">无文件</span>\n              <el-button size=\"small\" @click=\"selectFile('coreBlocks')\">选择文件</el-button>\n              <el-button v-if=\"formData.files.coreBlocks\" size=\"small\" type=\"danger\" @click=\"removeFile('coreBlocks')\">删除</el-button>\n            </div>\n          </div>\n\n          <!-- miRNA -->\n          <div class=\"file-item\">\n            <label>miRNA:</label>\n            <div class=\"file-controls\">\n              <span v-if=\"formData.files.miRNA\" class=\"current-file\">{{ formData.files.miRNA }}</span>\n              <span v-else class=\"no-file\">无文件</span>\n              <el-button size=\"small\" @click=\"selectFile('miRNA')\">选择文件</el-button>\n              <el-button v-if=\"formData.files.miRNA\" size=\"small\" type=\"danger\" @click=\"removeFile('miRNA')\">删除</el-button>\n            </div>\n          </div>\n\n          <!-- tRNA -->\n          <div class=\"file-item\">\n            <label>tRNA:</label>\n            <div class=\"file-controls\">\n              <span v-if=\"formData.files.tRNA\" class=\"current-file\">{{ formData.files.tRNA }}</span>\n              <span v-else class=\"no-file\">无文件</span>\n              <el-button size=\"small\" @click=\"selectFile('tRNA')\">选择文件</el-button>\n              <el-button v-if=\"formData.files.tRNA\" size=\"small\" type=\"danger\" @click=\"removeFile('tRNA')\">删除</el-button>\n            </div>\n          </div>\n\n          <!-- rRNA -->\n          <div class=\"file-item\">\n            <label>rRNA:</label>\n            <div class=\"file-controls\">\n              <span v-if=\"formData.files.rRNA\" class=\"current-file\">{{ formData.files.rRNA }}</span>\n              <span v-else class=\"no-file\">无文件</span>\n              <el-button size=\"small\" @click=\"selectFile('rRNA')\">选择文件</el-button>\n              <el-button v-if=\"formData.files.rRNA\" size=\"small\" type=\"danger\" @click=\"removeFile('rRNA')\">删除</el-button>\n            </div>\n          </div>\n        </div>\n      </el-form>\n      \n      <template #footer>\n        <span class=\"dialog-footer\">\n          <el-button @click=\"showAddDialog = false\">取消</el-button>\n          <el-button type=\"primary\" @click=\"saveData\" :loading=\"saving\">保存</el-button>\n        </span>\n      </template>\n    </el-dialog>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { ref, reactive, onMounted } from 'vue'\nimport { ElMessage, ElMessageBox } from 'element-plus'\nimport { Plus, Refresh, Download, Upload, Delete, Document } from '@element-plus/icons-vue'\nimport axios from 'axios'\n\nexport default {\n  name: 'AdminDataManager',\n  components: {\n    Plus,\n    Refresh,\n    Download,\n    Upload,\n    Delete,\n    Document\n  },\n  setup() {\n    const loading = ref(false)\n    const saving = ref(false)\n    const tableData = ref([])\n    const currentPage = ref(1)\n    const pageSize = ref(20)\n    const totalCount = ref(0)\n\n    const showAddDialog = ref(false)\n    const editingRow = ref(null)\n    const formRef = ref(null)\n\n    // 搜索和筛选相关\n    const searchAccession = ref('')\n    const selectedSubPopulations = ref([])\n    const allAccessionOptions = ref([])\n    const loadingAccessions = ref(false)\n\n    // 多选相关\n    const selectedRows = ref([])\n\n    const subPopulationOptions = ref([\n      { label: 'cA', value: 'cA' },\n      { label: 'cB', value: 'cB' },\n      { label: 'GJ', value: 'GJ' },\n      { label: 'XI', value: 'XI' },\n      { label: 'WILD', value: 'WILD' },\n      { label: 'O.glaberrima', value: 'O.glaberrima' },\n      { label: '未知', value: '-' }\n    ])\n    \n    const formData = reactive({\n      accession: '',\n      subPopulation: '',\n      seqData: '',\n      longitude: null,\n      latitude: null,\n      files: {\n        genome: '',\n        annotation: '',\n        transcriptomeAll: '',\n        transcriptomeLeaf: '',\n        transcriptomePanicles: '',\n        transcriptomeShoot: '',\n        transcriptomeStem: '',\n        transcriptomeRoot: '',\n        codon: '',\n        centromere: '',\n        TEs: '',\n        coreBlocks: '',\n        miRNA: '',\n        tRNA: '',\n        rRNA: ''\n      },\n      filesToUpload: {} // 存储待上传的文件\n    })\n    \n    const formRules = {\n      accession: [\n        { required: true, message: '请输入Accession', trigger: 'blur' }\n      ]\n    }\n\n    // 加载亚群选项\n    const loadSubPopulationOptions = async () => {\n      try {\n        const response = await axios.get('/admin/data-management/list/', {\n          params: { page: 1, page_size: 1000 } // 获取所有数据来提取亚群\n        })\n\n        if (response.data.success && response.data.data) {\n          // 提取所有唯一的亚群\n          const existingSubPopulations = new Set()\n          response.data.data.forEach(item => {\n            if (item.subPopulation && item.subPopulation !== '-') {\n              existingSubPopulations.add(item.subPopulation)\n            }\n          })\n\n          // 合并默认选项和已存在的亚群\n          const defaultOptions = [\n            { label: 'cA', value: 'cA' },\n            { label: 'cB', value: 'cB' },\n            { label: 'GJ', value: 'GJ' },\n            { label: 'XI', value: 'XI' },\n            { label: 'WILD', value: 'WILD' },\n            { label: 'O.glaberrima', value: 'O.glaberrima' },\n            { label: '未知', value: '-' }\n          ]\n\n          const defaultValues = new Set(defaultOptions.map(opt => opt.value))\n          const additionalOptions = Array.from(existingSubPopulations)\n            .filter(value => !defaultValues.has(value))\n            .map(value => ({ label: value, value: value }))\n\n          subPopulationOptions.value = [...defaultOptions, ...additionalOptions]\n        }\n      } catch (error) {\n        console.error('加载亚群选项失败:', error)\n      }\n    }\n\n    // 加载数据\n    const loadData = async () => {\n      try {\n        loading.value = true\n        const params = {\n          page: currentPage.value,\n          page_size: pageSize.value\n        }\n\n        // 添加搜索参数\n        if (searchAccession.value) {\n          params.search = searchAccession.value\n        }\n\n        // 添加亚群筛选参数\n        if (selectedSubPopulations.value.length > 0) {\n          params.sub_populations = selectedSubPopulations.value.join(',')\n        }\n\n        const response = await axios.get('/admin/data-management/list/', { params })\n        const data = response.data\n\n        if (data.success) {\n          tableData.value = data.data || []\n          totalCount.value = data.total || 0\n\n          // 调试信息：检查第一条记录的文件状态\n          if (data.data && data.data.length > 0) {\n            console.log('第一条记录的文件状态:', data.data[0])\n          }\n        } else {\n          throw new Error(data.message || '获取数据失败')\n        }\n\n      } catch (error) {\n        console.error('加载数据失败:', error)\n        ElMessage.error('加载数据失败')\n      } finally {\n        loading.value = false\n      }\n    }\n\n    // 刷新数据\n    const refreshData = () => {\n      loadData()\n    }\n\n    // 处理亚群变化（表单中的）\n    const handleSubPopulationChange = (value) => {\n      // 如果是新输入的亚群，添加到选项列表中\n      if (value && !subPopulationOptions.value.find(opt => opt.value === value)) {\n        subPopulationOptions.value.push({\n          label: value,\n          value: value\n        })\n      }\n    }\n\n    // 加载所有Accession选项\n    const loadAllAccessions = async () => {\n      try {\n        loadingAccessions.value = true\n        const response = await axios.get('/admin/data-management/list/', {\n          params: {\n            page: 1,\n            page_size: 1000  // 获取所有数据\n          }\n        })\n\n        if (response.data.success) {\n          allAccessionOptions.value = response.data.data.map(item => item.accession).sort()\n        }\n      } catch (error) {\n        console.error('加载Accession选项失败:', error)\n      } finally {\n        loadingAccessions.value = false\n      }\n    }\n\n    // 处理Accession选择变化\n    const handleAccessionChange = (value) => {\n      searchAccession.value = value\n      currentPage.value = 1\n      loadData()\n    }\n\n    // 清除Accession搜索\n    const handleAccessionClear = () => {\n      searchAccession.value = ''\n      currentPage.value = 1\n      loadData()\n    }\n\n    // 处理SubPopulation筛选变化\n    const handleSubPopulationFilterChange = () => {\n      currentPage.value = 1\n      loadData()\n    }\n\n    // 重置筛选\n    const resetFilters = () => {\n      searchAccession.value = ''\n      selectedSubPopulations.value = []\n      currentPage.value = 1\n      loadData()\n    }\n\n    // 处理表格选择变化\n    const handleSelectionChange = (selection) => {\n      selectedRows.value = selection\n    }\n\n    // 批量删除\n    const handleBatchDelete = async () => {\n      if (selectedRows.value.length === 0) {\n        ElMessage.warning('请先选择要删除的记录')\n        return\n      }\n\n      try {\n        const accessions = selectedRows.value.map(row => row.accession)\n        const message = `确定要删除选中的 ${accessions.length} 个 Accession 吗？\\n\\n${accessions.join(', ')}\\n\\n此操作将同时删除相关的所有数据文件，且无法恢复！`\n\n        await ElMessageBox.confirm(message, '批量删除确认', {\n          confirmButtonText: '确定删除',\n          cancelButtonText: '取消',\n          type: 'warning',\n          dangerouslyUseHTMLString: false\n        })\n\n        // 调用批量删除API\n        const response = await axios.post('/admin/data-management/batch-delete/', {\n          accessions: accessions\n        })\n\n        if (response.data.success) {\n          ElMessage.success(`成功删除 ${accessions.length} 个 Accession`)\n          selectedRows.value = [] // 清空选择\n          loadData()\n          loadAllAccessions() // 重新加载Accession选项\n        } else {\n          ElMessage.error(response.data.message || '批量删除失败')\n        }\n\n      } catch (error) {\n        if (error !== 'cancel') {\n          console.error('批量删除失败:', error)\n          ElMessage.error('批量删除失败')\n        }\n      }\n    }\n\n    // 更新数据（重新扫描文件）\n    const handleUpdateData = async () => {\n      try {\n        loading.value = true\n        const response = await axios.post('/admin/rescan/')\n\n        if (response.data.success) {\n          ElMessage.success(response.data.message)\n          loadData()\n        } else {\n          ElMessage.error(response.data.message)\n        }\n      } catch (error) {\n        console.error('更新数据失败:', error)\n        ElMessage.error('更新数据失败')\n      } finally {\n        loading.value = false\n      }\n    }\n\n    // 分页处理\n    const handleSizeChange = (size) => {\n      pageSize.value = size\n      currentPage.value = 1\n      loadData()\n    }\n\n    const handleCurrentChange = (page) => {\n      currentPage.value = page\n      loadData()\n    }\n\n    // 编辑行\n    const editRow = (row) => {\n      editingRow.value = row\n      formData.accession = row.accession\n      formData.subPopulation = row.subPopulation || ''\n      formData.seqData = row.seqData === '-' ? '' : (row.seqData || '')\n      formData.longitude = row.longitude\n      formData.latitude = row.latitude\n\n      // 加载文件信息\n      formData.files.genome = row.genomeFile || ''\n      formData.files.annotation = row.annotationFile || ''\n      formData.files.transcriptomeAll = row.transcriptomeAllFile || ''\n      formData.files.transcriptomeLeaf = row.transcriptomeLeafFile || ''\n      formData.files.transcriptomePanicles = row.transcriptomePaniclesFile || ''\n      formData.files.transcriptomeShoot = row.transcriptomeShootFile || ''\n      formData.files.transcriptomeStem = row.transcriptomeStemFile || ''\n      formData.files.transcriptomeRoot = row.transcriptomeRootFile || ''\n      formData.files.codon = row.codonFile || ''\n      formData.files.centromere = row.centromereFile || ''\n      formData.files.TEs = row.tesFile || ''\n      formData.files.coreBlocks = row.coreBlocksFile || ''\n      formData.files.miRNA = row.miRNAFile || ''\n      formData.files.tRNA = row.tRNAFile || ''\n      formData.files.rRNA = row.rRNAFile || ''\n\n      formData.filesToUpload = {} // 清空待上传文件\n      showAddDialog.value = true\n    }\n\n    // 删除行\n    const deleteRow = async (row) => {\n      try {\n        await ElMessageBox.confirm(\n          `确定要删除 Accession \"${row.accession}\" 吗？这将删除该条目的所有相关数据。`,\n          '确认删除',\n          {\n            confirmButtonText: '确定',\n            cancelButtonText: '取消',\n            type: 'warning'\n          }\n        )\n        \n        // 调用删除API\n        await axios.delete(`/admin/data-management/accession/${row.accession}/delete/`)\n        \n        ElMessage.success('删除成功')\n        loadData()\n        loadAllAccessions() // 重新加载Accession选项\n        \n      } catch (error) {\n        if (error !== 'cancel') {\n          console.error('删除失败:', error)\n          ElMessage.error('删除失败')\n        }\n      }\n    }\n\n    // 保存数据\n    const saveData = async () => {\n      try {\n        await formRef.value.validate()\n\n        saving.value = true\n\n        const data = {\n          accession: formData.accession,\n          subPopulation: formData.subPopulation || '-',\n          seqData: formData.seqData || '-',\n          longitude: formData.longitude,\n          latitude: formData.latitude\n        }\n\n        // 先保存基本信息\n        if (editingRow.value) {\n          // 编辑\n          await axios.put(`/admin/data-management/accession/${editingRow.value.accession}/update/`, data)\n        } else {\n          // 新增\n          await axios.post('/admin/data-management/accession/', data)\n        }\n\n        // 处理文件上传\n        const accession = formData.accession\n\n        // 文件类型映射：前端字段名 -> API参数\n        const fileTypeMapping = {\n          'genome': 'genome',\n          'annotation': 'annotation',\n          'transcriptomeAll': 'transcriptome.all',\n          'transcriptomeLeaf': 'transcriptome.leaf',\n          'transcriptomePanicles': 'transcriptome.panicles',\n          'transcriptomeShoot': 'transcriptome.shoot',\n          'transcriptomeStem': 'transcriptome.stem',\n          'transcriptomeRoot': 'transcriptome.root',\n          'codon': 'codon',\n          'centromere': 'centromere',\n          'TEs': 'TEs',\n          'coreBlocks': 'coreBlocks',\n          'miRNA': 'miRNA',\n          'tRNA': 'tRNA',\n          'rRNA': 'rRNA'\n        }\n\n        for (const [frontendFileType, file] of Object.entries(formData.filesToUpload)) {\n          if (file) {\n            try {\n              const apiFileType = fileTypeMapping[frontendFileType] || frontendFileType\n              const fileFormData = new FormData()\n              fileFormData.append('file', file)\n              fileFormData.append('accession', accession)\n              fileFormData.append('fileType', apiFileType)\n\n              await axios.post('/admin/data-management/upload-file/', fileFormData, {\n                headers: {\n                  'Content-Type': 'multipart/form-data'\n                }\n              })\n            } catch (fileError) {\n              console.error(`文件 ${frontendFileType} 上传失败:`, fileError)\n              ElMessage.warning(`文件 ${frontendFileType} 上传失败`)\n            }\n          }\n        }\n\n        // 处理文件删除（如果文件名被清空但原来有文件）\n        if (editingRow.value) {\n          const fileTypeMappings = [\n            { formKey: 'genome', apiKey: 'genome', originalKey: 'genomeFile' },\n            { formKey: 'annotation', apiKey: 'annotation', originalKey: 'annotationFile' },\n            { formKey: 'transcriptomeAll', apiKey: 'transcriptome.all', originalKey: 'transcriptomeAllFile' },\n            { formKey: 'transcriptomeLeaf', apiKey: 'transcriptome.leaf', originalKey: 'transcriptomeLeafFile' },\n            { formKey: 'transcriptomePanicles', apiKey: 'transcriptome.panicles', originalKey: 'transcriptomePaniclesFile' },\n            { formKey: 'transcriptomeShoot', apiKey: 'transcriptome.shoot', originalKey: 'transcriptomeShootFile' },\n            { formKey: 'transcriptomeStem', apiKey: 'transcriptome.stem', originalKey: 'transcriptomeStemFile' },\n            { formKey: 'transcriptomeRoot', apiKey: 'transcriptome.root', originalKey: 'transcriptomeRootFile' },\n            { formKey: 'codon', apiKey: 'codon', originalKey: 'codonFile' },\n            { formKey: 'centromere', apiKey: 'centromere', originalKey: 'centromereFile' },\n            { formKey: 'TEs', apiKey: 'TEs', originalKey: 'tesFile' },\n            { formKey: 'coreBlocks', apiKey: 'coreBlocks', originalKey: 'coreBlocksFile' },\n            { formKey: 'miRNA', apiKey: 'miRNA', originalKey: 'miRNAFile' },\n            { formKey: 'tRNA', apiKey: 'tRNA', originalKey: 'tRNAFile' },\n            { formKey: 'rRNA', apiKey: 'rRNA', originalKey: 'rRNAFile' }\n          ]\n\n          for (const mapping of fileTypeMappings) {\n            const originalFile = editingRow.value[mapping.originalKey]\n            const currentFile = formData.files[mapping.formKey]\n\n            // 如果原来有文件，现在没有，且没有新上传的文件，则删除\n            if (originalFile && !currentFile && !formData.filesToUpload[mapping.formKey]) {\n              try {\n                await axios.delete(`/admin/data-management/delete-file/${accession}/${mapping.apiKey}/`)\n              } catch (deleteError) {\n                console.error(`文件 ${mapping.apiKey} 删除失败:`, deleteError)\n              }\n            }\n          }\n        }\n\n        ElMessage.success(editingRow.value ? '更新成功' : '新增成功')\n        showAddDialog.value = false\n        loadData()\n        loadSubPopulationOptions() // 重新加载亚群选项\n        loadAllAccessions() // 重新加载Accession选项\n\n      } catch (error) {\n        console.error('保存失败:', error)\n        ElMessage.error('保存失败')\n      } finally {\n        saving.value = false\n      }\n    }\n\n    // 重置表单\n    const resetForm = () => {\n      editingRow.value = null\n      formData.accession = ''\n      formData.subPopulation = ''\n      formData.seqData = ''\n      formData.longitude = null\n      formData.latitude = null\n\n      // 重置文件信息\n      formData.files = {\n        genome: '',\n        annotation: '',\n        transcriptomeAll: '',\n        transcriptomeLeaf: '',\n        transcriptomePanicles: '',\n        transcriptomeShoot: '',\n        transcriptomeStem: '',\n        transcriptomeRoot: '',\n        codon: '',\n        centromere: '',\n        TEs: '',\n        coreBlocks: '',\n        miRNA: '',\n        tRNA: '',\n        rRNA: ''\n      }\n      formData.filesToUpload = {}\n\n      if (formRef.value) {\n        formRef.value.clearValidate()\n      }\n    }\n\n    // 文件选择方法（用于编辑对话框）\n    const selectFile = (fileType) => {\n      const input = document.createElement('input')\n      input.type = 'file'\n\n      // 根据文件类型设置接受的文件格式\n      const fileExtensions = {\n        'genome': '.fasta,.fa,.fas',\n        'annotation': '.gff,.gff3',\n        'transcriptomeAll': '.tar.gz',\n        'transcriptomeLeaf': '.tar.gz',\n        'transcriptomePanicles': '.tar.gz',\n        'transcriptomeShoot': '.tar.gz',\n        'transcriptomeStem': '.tar.gz',\n        'transcriptomeRoot': '.tar.gz',\n        'codon': '.tar.gz',\n        'centromere': '.bed',\n        'TEs': '.tar.gz',\n        'coreBlocks': '.bed',\n        'miRNA': '.bed',\n        'tRNA': '.bed',\n        'rRNA': '.bed'\n      }\n\n      input.accept = fileExtensions[fileType] || '*'\n\n      input.onchange = (event) => {\n        const file = event.target.files[0]\n        if (file) {\n          formData.files[fileType] = file.name\n          formData.filesToUpload[fileType] = file\n        }\n      }\n\n      input.click()\n    }\n\n    // 移除文件方法（用于编辑对话框）\n    const removeFile = (fileType) => {\n      formData.files[fileType] = ''\n      delete formData.filesToUpload[fileType]\n    }\n\n    // 文件操作方法（用于表格中的下载）\n    const uploadFile = (accession, fileType) => {\n      // 创建文件输入元素\n      const input = document.createElement('input')\n      input.type = 'file'\n\n      // 根据文件类型设置接受的文件格式\n      const fileExtensions = {\n        'genome': '.fasta,.fa,.fas',\n        'annotation': '.gff,.gff3',\n        'transcriptome.all': '.tar.gz',\n        'transcriptome.leaf': '.tar.gz',\n        'transcriptome.panicles': '.tar.gz',\n        'transcriptome.shoot': '.tar.gz',\n        'transcriptome.stem': '.tar.gz',\n        'transcriptome.root': '.tar.gz',\n        'codon': '.tar.gz',\n        'centromere': '.bed',\n        'TEs': '.tar.gz',\n        'coreBlocks': '.bed',\n        'miRNA': '.bed',\n        'tRNA': '.bed',\n        'rRNA': '.bed'\n      }\n\n      input.accept = fileExtensions[fileType] || '*'\n\n      input.onchange = async (event) => {\n        const file = event.target.files[0]\n        if (!file) return\n\n        try {\n          const formData = new FormData()\n          formData.append('file', file)\n          formData.append('accession', accession)\n          formData.append('fileType', fileType)\n\n          const response = await axios.post('/admin/data-management/upload-file/', formData, {\n            headers: {\n              'Content-Type': 'multipart/form-data'\n            }\n          })\n\n          if (response.data.success) {\n            ElMessage.success(`${fileType} 文件上传成功`)\n            loadData() // 刷新数据\n          } else {\n            ElMessage.error(response.data.message || '上传失败')\n          }\n        } catch (error) {\n          console.error('文件上传失败:', error)\n          ElMessage.error('文件上传失败')\n        }\n      }\n\n      input.click()\n    }\n\n    const downloadFile = async (accession, fileType) => {\n      try {\n        const response = await axios.get(`/admin/data-management/download-file/${accession}/${fileType}/`, {\n          responseType: 'blob'\n        })\n\n        // 创建下载链接\n        const url = window.URL.createObjectURL(new Blob([response.data]))\n        const link = document.createElement('a')\n        link.href = url\n\n        // 根据文件类型设置文件名\n        const extensions = {\n          'genome': 'fasta',\n          'annotation': 'gff',\n          'transcriptome': 'tar.gz',\n          'codon': 'tar.gz',\n          'centromere': 'bed',\n          'TEs': 'tar.gz',\n          'coreBlocks': 'bed',\n          'miRNA': 'bed',\n          'tRNA': 'bed',\n          'rRNA': 'bed'\n        }\n\n        link.download = `${fileType}.${accession}.${extensions[fileType]}`\n        document.body.appendChild(link)\n        link.click()\n        document.body.removeChild(link)\n        window.URL.revokeObjectURL(url)\n\n      } catch (error) {\n        console.error('文件下载失败:', error)\n        ElMessage.error('文件下载失败')\n      }\n    }\n\n    const deleteFile = async (accession, fileType) => {\n      try {\n        await ElMessageBox.confirm(\n          `确定要删除 ${accession} 的 ${fileType} 文件吗？`,\n          '确认删除',\n          {\n            confirmButtonText: '确定',\n            cancelButtonText: '取消',\n            type: 'warning'\n          }\n        )\n\n        const response = await axios.delete(`/admin/data-management/delete-file/${accession}/${fileType}/`)\n\n        if (response.data.success) {\n          ElMessage.success(`${fileType} 文件删除成功`)\n          loadData() // 刷新数据\n        } else {\n          ElMessage.error(response.data.message || '删除失败')\n        }\n\n      } catch (error) {\n        if (error !== 'cancel') {\n          console.error('文件删除失败:', error)\n          ElMessage.error('文件删除失败')\n        }\n      }\n    }\n\n    onMounted(() => {\n      loadData()\n      loadSubPopulationOptions()\n      loadAllAccessions()\n    })\n\n    return {\n      loading,\n      saving,\n      tableData,\n      currentPage,\n      pageSize,\n      totalCount,\n      showAddDialog,\n      editingRow,\n      formRef,\n      formData,\n      formRules,\n      subPopulationOptions,\n      // 搜索和筛选相关\n      searchAccession,\n      selectedSubPopulations,\n      allAccessionOptions,\n      loadingAccessions,\n      loadAllAccessions,\n      handleAccessionChange,\n      handleAccessionClear,\n      handleSubPopulationFilterChange,\n      resetFilters,\n      // 多选相关\n      selectedRows,\n      handleSelectionChange,\n      handleBatchDelete,\n      // 原有方法\n      loadData,\n      refreshData,\n      handleUpdateData,\n      handleSizeChange,\n      handleCurrentChange,\n      editRow,\n      deleteRow,\n      saveData,\n      resetForm,\n      selectFile,\n      removeFile,\n      uploadFile,\n      downloadFile,\n      deleteFile\n    }\n  }\n}\n</script>\n\n<style scoped>\n.admin-data-manager {\n  padding: 20px;\n}\n\n.page-header {\n  margin-bottom: 20px;\n}\n\n.page-header h2 {\n  margin: 0 0 10px 0;\n  color: #1a56db;\n  font-size: 24px;\n  font-weight: 600;\n}\n\n.header-stats {\n  color: #666;\n  font-size: 14px;\n  display: flex;\n  align-items: center;\n  gap: 15px;\n}\n\n.header-stats span {\n  white-space: nowrap;\n}\n\n.selected-info {\n  color: #409eff;\n  font-weight: 500;\n}\n\n/* 工具栏样式 */\n.toolbar-container {\n  background: white;\n  padding: 15px 20px;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n  margin-bottom: 20px;\n}\n\n.toolbar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.toolbar-left {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n}\n\n.toolbar-right {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n}\n\n.file-link {\n  color: #1a56db;\n  cursor: pointer;\n  text-decoration: none;\n  word-break: break-all;\n  font-size: 12px;\n}\n\n.file-link:hover {\n  text-decoration: underline;\n}\n\n.file-management-grid {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 15px;\n  margin-top: 10px;\n}\n\n.file-item {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.file-item label {\n  font-weight: bold;\n  color: #333;\n  font-size: 14px;\n}\n\n.file-controls {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  flex-wrap: wrap;\n}\n\n.current-file {\n  color: #67c23a;\n  font-size: 12px;\n  word-break: break-all;\n  flex: 1;\n  min-width: 0;\n}\n\n.no-file {\n  color: #999;\n  font-size: 12px;\n}\n\n.data-link {\n  color: #1a56db;\n  text-decoration: none;\n  word-break: break-all;\n}\n\n.data-link:hover {\n  text-decoration: underline;\n}\n\n.data-empty {\n  color: #999;\n}\n\n.pagination-container {\n  margin-top: 20px;\n  display: flex;\n  justify-content: center;\n}\n\n.dialog-footer {\n  display: flex;\n  justify-content: flex-end;\n  gap: 10px;\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAoB;;EAExBA,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAa;;EAEjBA,KAAK,EAAC;AAAc;;;;;;EAKcA,KAAK,EAAC;;;EAO1CA,KAAK,EAAC;AAAmB;;EACzBA,KAAK,EAAC;AAAS;;EACbA,KAAK,EAAC;AAAc;;EAsCpBA,KAAK,EAAC;AAAe;;EAsBzBA,KAAK,EAAC;AAAoB;;;;EAwBZA,KAAK,EAAC;;;;;EAeNA,KAAK,EAAC;;;;;EAYNA,KAAK,EAAC;;;;;EAYNA,KAAK,EAAC;;;;;EAYNA,KAAK,EAAC;;;;;EAYNA,KAAK,EAAC;;;;;EAYNA,KAAK,EAAC;;;;;EAYNA,KAAK,EAAC;;;;;EAYNA,KAAK,EAAC;;;;;EAYNA,KAAK,EAAC;;;;;EAYNA,KAAK,EAAC;;;;;EAYNA,KAAK,EAAC;;;;;EAYNA,KAAK,EAAC;;;;;EAYNA,KAAK,EAAC;;;;;EAYNA,KAAK,EAAC;;;;;EAYNA,KAAK,EAAC;;;EAapBA,KAAK,EAAC;AAAsB;;EAwDxBA,KAAK,EAAC;AAAsB;;EAE1BA,KAAK,EAAC;AAAW;;EAEfA,KAAK,EAAC;AAAe;;;EACWA,KAAK,EAAC;;;;EAC5BA,KAAK,EAAC;;;EAOlBA,KAAK,EAAC;AAAW;;EAEfA,KAAK,EAAC;AAAe;;;EACeA,KAAK,EAAC;;;;EAChCA,KAAK,EAAC;;;EAOlBA,KAAK,EAAC;AAAW;;EAEfA,KAAK,EAAC;AAAe;;;EACqBA,KAAK,EAAC;;;;EACtCA,KAAK,EAAC;;;EAOlBA,KAAK,EAAC;AAAW;;EAEfA,KAAK,EAAC;AAAe;;;EACsBA,KAAK,EAAC;;;;EACvCA,KAAK,EAAC;;;EAOlBA,KAAK,EAAC;AAAW;;EAEfA,KAAK,EAAC;AAAe;;;EAC0BA,KAAK,EAAC;;;;EAC3CA,KAAK,EAAC;;;EAOlBA,KAAK,EAAC;AAAW;;EAEfA,KAAK,EAAC;AAAe;;;EACuBA,KAAK,EAAC;;;;EACxCA,KAAK,EAAC;;;EAOlBA,KAAK,EAAC;AAAW;;EAEfA,KAAK,EAAC;AAAe;;;EACsBA,KAAK,EAAC;;;;EACvCA,KAAK,EAAC;;;EAOlBA,KAAK,EAAC;AAAW;;EAEfA,KAAK,EAAC;AAAe;;;EACsBA,KAAK,EAAC;;;;EACvCA,KAAK,EAAC;;;EAOlBA,KAAK,EAAC;AAAW;;EAEfA,KAAK,EAAC;AAAe;;;EACUA,KAAK,EAAC;;;;EAC3BA,KAAK,EAAC;;;EAOlBA,KAAK,EAAC;AAAW;;EAEfA,KAAK,EAAC;AAAe;;;EACeA,KAAK,EAAC;;;;EAChCA,KAAK,EAAC;;;EAOlBA,KAAK,EAAC;AAAW;;EAEfA,KAAK,EAAC;AAAe;;;EACQA,KAAK,EAAC;;;;EACzBA,KAAK,EAAC;;;EAOlBA,KAAK,EAAC;AAAW;;EAEfA,KAAK,EAAC;AAAe;;;EACeA,KAAK,EAAC;;;;EAChCA,KAAK,EAAC;;;EAOlBA,KAAK,EAAC;AAAW;;EAEfA,KAAK,EAAC;AAAe;;;EACUA,KAAK,EAAC;;;;EAC3BA,KAAK,EAAC;;;EAOlBA,KAAK,EAAC;AAAW;;EAEfA,KAAK,EAAC;AAAe;;;EACSA,KAAK,EAAC;;;;EAC1BA,KAAK,EAAC;;;EAOlBA,KAAK,EAAC;AAAW;;EAEfA,KAAK,EAAC;AAAe;;;EACSA,KAAK,EAAC;;;;EAC1BA,KAAK,EAAC;;;EASnBA,KAAK,EAAC;AAAe;;;;;;;;;;;;;;;;;;;uBA5gBjCC,mBAAA,CAmhBM,OAnhBNC,UAmhBM,GAlhBJC,mBAAA,iBAAoB,EACpBC,mBAAA,CA0EM,OA1ENC,UA0EM,GAzEJD,mBAAA,CAWM,OAXNE,UAWM,G,4BAVJF,mBAAA,CAAe,YAAX,QAAM,qBACVA,mBAAA,CAQM,OARNG,UAQM,GAPJH,mBAAA,CAAqC,cAA/B,MAAI,GAAAI,gBAAA,CAAGC,MAAA,CAAAC,UAAU,IAAG,MAAI,iBAClBD,MAAA,CAAAE,SAAS,CAACC,MAAM,Q,cAA5BX,mBAAA,CAEO,QAAAY,UAAA,EAF2B,SAC1B,GAAAL,gBAAA,CAAGC,MAAA,CAAAE,SAAS,CAACC,MAAM,IAAG,MAC9B,mB,mCACYH,MAAA,CAAAK,YAAY,CAACF,MAAM,Q,cAA/BX,mBAAA,CAEO,QAFPc,UAEO,EAFoD,QACpD,GAAAP,gBAAA,CAAGC,MAAA,CAAAK,YAAY,CAACF,MAAM,IAAG,KAChC,mB,uCAIJT,mBAAA,WAAc,EACdC,mBAAA,CA0DI,OA1DJY,UA0DI,GAzDJZ,mBAAA,CAwDM,OAxDNa,UAwDM,GAvDJb,mBAAA,CAoCM,OApCNc,UAoCM,GAnCJC,YAAA,CAaYC,oBAAA;gBAZDX,MAAA,CAAAY,eAAe;+DAAfZ,MAAA,CAAAY,eAAe,GAAAC,MAAA;IACxBC,WAAW,EAAC,cAAc;IAC1BC,UAAU,EAAV,EAAU;IACVC,SAAS,EAAT,EAAS;IACTC,KAAyC,EAAzC;MAAA;MAAA;IAAA,CAAyC;IACxCC,QAAM,EAAElB,MAAA,CAAAmB,qBAAqB;IAC7BC,OAAK,EAAEpB,MAAA,CAAAqB;;sBAEN,MAAwC,E,kBAD1C7B,mBAAA,CAIuB8B,SAAA,QAAAC,WAAA,CAHDvB,MAAA,CAAAwB,mBAAmB,EAAhCC,SAAS;2BADlBC,YAAA,CAIuBC,oBAAA;QAFpBC,GAAG,EAAEH,SAAS;QACdI,KAAK,EAAEJ,SAAS;QAChBK,KAAK,EAAEL;;;;4DAGZf,YAAA,CAcYC,oBAAA;gBAbDX,MAAA,CAAA+B,sBAAsB;+DAAtB/B,MAAA,CAAA+B,sBAAsB,GAAAlB,MAAA;IAC/BC,WAAW,EAAC,OAAO;IACnBkB,QAAQ,EAAR,EAAQ;IACR,eAAa,EAAb,EAAa;IACb,uBAAqB,EAArB,EAAqB;IACrBhB,SAAS,EAAT,EAAS;IACTC,KAAyC,EAAzC;MAAA;MAAA;IAAA,CAAyC;IACxCC,QAAM,EAAElB,MAAA,CAAAiC;;sBAEP,MAAsC,E,kBADxCzC,mBAAA,CAI0B8B,SAAA,QAAAC,WAAA,CAHPvB,MAAA,CAAAkC,oBAAoB,EAA9BC,MAAM;2BADfT,YAAA,CAI0BC,oBAAA;QAFvBC,GAAG,EAAEO,MAAM,CAACL,KAAK;QACjBD,KAAK,EAAEM,MAAM,CAACN,KAAK;QACnBC,KAAK,EAAEK,MAAM,CAACL;;;;iDAGnBpB,YAAA,CAGY0B,oBAAA;IAHAC,OAAK,EAAErC,MAAA,CAAAsC;EAAY;sBAC7B,MAA8B,CAA9B5B,YAAA,CAA8B6B,kBAAA;wBAArB,MAAW,CAAX7B,YAAA,CAAW8B,kBAAA,E;;qDAAU,QAEhC,G;;;oCAGF7C,mBAAA,CAgBM,OAhBN8C,WAgBM,GAfJ/B,YAAA,CAMY0B,oBAAA;IALVM,IAAI,EAAC,QAAQ;IACZC,QAAQ,EAAE3C,MAAA,CAAAK,YAAY,CAACF,MAAM;IAC7BkC,OAAK,EAAErC,MAAA,CAAA4C;;sBACR,MAA6B,CAA7BlC,YAAA,CAA6B6B,kBAAA;wBAApB,MAAU,CAAV7B,YAAA,CAAUmC,iBAAA,E;;yBAAU,SACvB,GAAA9C,gBAAA,CAAGC,MAAA,CAAAK,YAAY,CAACF,MAAM,IAAG,IACjC,gB;;8CACAO,YAAA,CAGY0B,oBAAA;IAHDM,IAAI,EAAC,SAAS;IAAEL,OAAK,EAAAS,MAAA,QAAAA,MAAA,MAAAjC,MAAA,IAAEb,MAAA,CAAA+C,aAAa;;sBAC7C,MAA2B,CAA3BrC,YAAA,CAA2B6B,kBAAA;wBAAlB,MAAQ,CAAR7B,YAAA,CAAQsC,eAAA,E;;qDAAU,gBAE7B,G;;;MACAtC,YAAA,CAGY0B,oBAAA;IAHDM,IAAI,EAAC,SAAS;IAAEL,OAAK,EAAErC,MAAA,CAAAiD;;sBAChC,MAA8B,CAA9BvC,YAAA,CAA8B6B,kBAAA;wBAArB,MAAW,CAAX7B,YAAA,CAAW8B,kBAAA,E;;qDAAU,QAEhC,G;;;0CAMN9C,mBAAA,cAAiB,EACjBC,mBAAA,CAmcM,OAncNuD,WAmcM,GAlcJxD,mBAAA,UAAa,E,+BACbgC,YAAA,CAuNSyB,mBAAA;IAtNRC,IAAI,EAAEpD,MAAA,CAAAE,SAAS;IAEhBmD,MAAM,EAAN,EAAM;IACNC,MAAM,EAAN,EAAM;IACNrC,KAAmB,EAAnB;MAAA;IAAA,CAAmB;IAClB,mBAAiB,EAAE;MAAAsC,UAAA;MAAAC,KAAA;MAAAC,UAAA;IAAA,CAA+D;IAClFC,iBAAgB,EAAE1D,MAAA,CAAA2D;;sBAEnB,MAAa,CAAbjE,mBAAA,UAAa,EACbgB,YAAA,CAA4DkD,0BAAA;MAA3ClB,IAAI,EAAC,WAAW;MAACmB,KAAK,EAAC,IAAI;MAACC,KAAK,EAAC;QAEnDpD,YAAA,CAA+EkD,0BAAA;MAA9DG,IAAI,EAAC,WAAW;MAAClC,KAAK,EAAC,WAAW;MAACgC,KAAK,EAAC,KAAK;MAACC,KAAK,EAAC;QACtEpD,YAAA,CAA0EkD,0BAAA;MAAzDG,IAAI,EAAC,eAAe;MAAClC,KAAK,EAAC,eAAe;MAACgC,KAAK,EAAC;QAClEnD,YAAA,CAUkBkD,0BAAA;MAVDG,IAAI,EAAC,SAAS;MAAClC,KAAK,EAAC,SAAS;MAACgC,KAAK,EAAC;;MACzCG,OAAO,EAAAC,QAAA,CAMZC,KANmB,KACdA,KAAK,CAACC,GAAG,CAACC,OAAO,IAAIF,KAAK,CAACC,GAAG,CAACC,OAAO,Y,cAA/C5E,mBAAA,CAKI;;QAJA6E,IAAI,EAAEH,KAAK,CAACC,GAAG,CAACC,OAAO;QACxBE,MAAM,EAAC,QAAQ;QACf/E,KAAK,EAAC;0BACJ2E,KAAK,CAACC,GAAG,CAACC,OAAO,wBAAAG,WAAA,M,cAEtB/E,mBAAA,CAAwC,QAAxCgF,WAAwC,EAAR,GAAC,G;;QAGrC9D,YAAA,CAAkEkD,0BAAA;MAAjDG,IAAI,EAAC,WAAW;MAAClC,KAAK,EAAC,WAAW;MAACgC,KAAK,EAAC;QAC1DnD,YAAA,CAAgEkD,0BAAA;MAA/CG,IAAI,EAAC,UAAU;MAAClC,KAAK,EAAC,UAAU;MAACgC,KAAK,EAAC;QAExDnE,mBAAA,WAAc,EACdgB,YAAA,CAUkBkD,0BAAA;MAVD/B,KAAK,EAAC,QAAQ;MAACgC,KAAK,EAAC;;MACzBG,OAAO,EAAAC,QAAA,CAMTC,KANgB,KACXA,KAAK,CAACC,GAAG,CAACM,UAAU,I,cAAhCjF,mBAAA,CAKO;;QAJDD,KAAK,EAAC,WAAW;QAChB8C,OAAK,EAAAxB,MAAA,IAAEb,MAAA,CAAA0E,YAAY,CAACR,KAAK,CAACC,GAAG,CAAC1C,SAAS;QACvCkD,KAAK,EAAET,KAAK,CAACC,GAAG,CAACM;0BACnBP,KAAK,CAACC,GAAG,CAACM,UAAU,wBAAAG,WAAA,M,cAEzBpF,mBAAA,CAAwC,QAAxCqF,WAAwC,EAAR,GAAC,G;;QAIrCnE,YAAA,CAUkBkD,0BAAA;MAVD/B,KAAK,EAAC,YAAY;MAACgC,KAAK,EAAC;;MAC7BG,OAAO,EAAAC,QAAA,CAMTC,KANgB,KACXA,KAAK,CAACC,GAAG,CAACW,cAAc,I,cAApCtF,mBAAA,CAKO;;QAJDD,KAAK,EAAC,WAAW;QAChB8C,OAAK,EAAAxB,MAAA,IAAEb,MAAA,CAAA0E,YAAY,CAACR,KAAK,CAACC,GAAG,CAAC1C,SAAS;QACvCkD,KAAK,EAAET,KAAK,CAACC,GAAG,CAACW;0BACnBZ,KAAK,CAACC,GAAG,CAACW,cAAc,wBAAAC,WAAA,M,cAE7BvF,mBAAA,CAAwC,QAAxCwF,WAAwC,EAAR,GAAC,G;;QAIrCtE,YAAA,CAUkBkD,0BAAA;MAVD/B,KAAK,EAAC,mBAAmB;MAACgC,KAAK,EAAC;;MACpCG,OAAO,EAAAC,QAAA,CAMTC,KANgB,KACXA,KAAK,CAACC,GAAG,CAACc,oBAAoB,I,cAA1CzF,mBAAA,CAKO;;QAJDD,KAAK,EAAC,WAAW;QAChB8C,OAAK,EAAAxB,MAAA,IAAEb,MAAA,CAAA0E,YAAY,CAACR,KAAK,CAACC,GAAG,CAAC1C,SAAS;QACvCkD,KAAK,EAAET,KAAK,CAACC,GAAG,CAACc;0BACnBf,KAAK,CAACC,GAAG,CAACc,oBAAoB,wBAAAC,WAAA,M,cAEnC1F,mBAAA,CAAwC,QAAxC2F,WAAwC,EAAR,GAAC,G;;QAIrCzE,YAAA,CAUkBkD,0BAAA;MAVD/B,KAAK,EAAC,oBAAoB;MAACgC,KAAK,EAAC;;MACrCG,OAAO,EAAAC,QAAA,CAMTC,KANgB,KACXA,KAAK,CAACC,GAAG,CAACiB,qBAAqB,I,cAA3C5F,mBAAA,CAKO;;QAJDD,KAAK,EAAC,WAAW;QAChB8C,OAAK,EAAAxB,MAAA,IAAEb,MAAA,CAAA0E,YAAY,CAACR,KAAK,CAACC,GAAG,CAAC1C,SAAS;QACvCkD,KAAK,EAAET,KAAK,CAACC,GAAG,CAACiB;0BACnBlB,KAAK,CAACC,GAAG,CAACiB,qBAAqB,wBAAAC,WAAA,M,cAEpC7F,mBAAA,CAAwC,QAAxC8F,WAAwC,EAAR,GAAC,G;;QAIrC5E,YAAA,CAUkBkD,0BAAA;MAVD/B,KAAK,EAAC,wBAAwB;MAACgC,KAAK,EAAC;;MACzCG,OAAO,EAAAC,QAAA,CAMTC,KANgB,KACXA,KAAK,CAACC,GAAG,CAACoB,yBAAyB,I,cAA/C/F,mBAAA,CAKO;;QAJDD,KAAK,EAAC,WAAW;QAChB8C,OAAK,EAAAxB,MAAA,IAAEb,MAAA,CAAA0E,YAAY,CAACR,KAAK,CAACC,GAAG,CAAC1C,SAAS;QACvCkD,KAAK,EAAET,KAAK,CAACC,GAAG,CAACoB;0BACnBrB,KAAK,CAACC,GAAG,CAACoB,yBAAyB,wBAAAC,WAAA,M,cAExChG,mBAAA,CAAwC,QAAxCiG,WAAwC,EAAR,GAAC,G;;QAIrC/E,YAAA,CAUkBkD,0BAAA;MAVD/B,KAAK,EAAC,qBAAqB;MAACgC,KAAK,EAAC;;MACtCG,OAAO,EAAAC,QAAA,CAMTC,KANgB,KACXA,KAAK,CAACC,GAAG,CAACuB,sBAAsB,I,cAA5ClG,mBAAA,CAKO;;QAJDD,KAAK,EAAC,WAAW;QAChB8C,OAAK,EAAAxB,MAAA,IAAEb,MAAA,CAAA0E,YAAY,CAACR,KAAK,CAACC,GAAG,CAAC1C,SAAS;QACvCkD,KAAK,EAAET,KAAK,CAACC,GAAG,CAACuB;0BACnBxB,KAAK,CAACC,GAAG,CAACuB,sBAAsB,wBAAAC,WAAA,M,cAErCnG,mBAAA,CAAwC,QAAxCoG,WAAwC,EAAR,GAAC,G;;QAIrClF,YAAA,CAUkBkD,0BAAA;MAVD/B,KAAK,EAAC,oBAAoB;MAACgC,KAAK,EAAC;;MACrCG,OAAO,EAAAC,QAAA,CAMTC,KANgB,KACXA,KAAK,CAACC,GAAG,CAAC0B,qBAAqB,I,cAA3CrG,mBAAA,CAKO;;QAJDD,KAAK,EAAC,WAAW;QAChB8C,OAAK,EAAAxB,MAAA,IAAEb,MAAA,CAAA0E,YAAY,CAACR,KAAK,CAACC,GAAG,CAAC1C,SAAS;QACvCkD,KAAK,EAAET,KAAK,CAACC,GAAG,CAAC0B;0BACnB3B,KAAK,CAACC,GAAG,CAAC0B,qBAAqB,wBAAAC,WAAA,M,cAEpCtG,mBAAA,CAAwC,QAAxCuG,WAAwC,EAAR,GAAC,G;;QAIrCrF,YAAA,CAUkBkD,0BAAA;MAVD/B,KAAK,EAAC,oBAAoB;MAACgC,KAAK,EAAC;;MACrCG,OAAO,EAAAC,QAAA,CAMTC,KANgB,KACXA,KAAK,CAACC,GAAG,CAAC6B,qBAAqB,I,cAA3CxG,mBAAA,CAKO;;QAJDD,KAAK,EAAC,WAAW;QAChB8C,OAAK,EAAAxB,MAAA,IAAEb,MAAA,CAAA0E,YAAY,CAACR,KAAK,CAACC,GAAG,CAAC1C,SAAS;QACvCkD,KAAK,EAAET,KAAK,CAACC,GAAG,CAAC6B;0BACnB9B,KAAK,CAACC,GAAG,CAAC6B,qBAAqB,wBAAAC,WAAA,M,cAEpCzG,mBAAA,CAAwC,QAAxC0G,WAAwC,EAAR,GAAC,G;;QAIrCxF,YAAA,CAUkBkD,0BAAA;MAVD/B,KAAK,EAAC,OAAO;MAACgC,KAAK,EAAC;;MACxBG,OAAO,EAAAC,QAAA,CAMTC,KANgB,KACXA,KAAK,CAACC,GAAG,CAACgC,SAAS,I,cAA/B3G,mBAAA,CAKO;;QAJDD,KAAK,EAAC,WAAW;QAChB8C,OAAK,EAAAxB,MAAA,IAAEb,MAAA,CAAA0E,YAAY,CAACR,KAAK,CAACC,GAAG,CAAC1C,SAAS;QACvCkD,KAAK,EAAET,KAAK,CAACC,GAAG,CAACgC;0BACnBjC,KAAK,CAACC,GAAG,CAACgC,SAAS,wBAAAC,WAAA,M,cAExB5G,mBAAA,CAAwC,QAAxC6G,WAAwC,EAAR,GAAC,G;;QAIrC3F,YAAA,CAUkBkD,0BAAA;MAVD/B,KAAK,EAAC,YAAY;MAACgC,KAAK,EAAC;;MAC7BG,OAAO,EAAAC,QAAA,CAMTC,KANgB,KACXA,KAAK,CAACC,GAAG,CAACmC,cAAc,I,cAApC9G,mBAAA,CAKO;;QAJDD,KAAK,EAAC,WAAW;QAChB8C,OAAK,EAAAxB,MAAA,IAAEb,MAAA,CAAA0E,YAAY,CAACR,KAAK,CAACC,GAAG,CAAC1C,SAAS;QACvCkD,KAAK,EAAET,KAAK,CAACC,GAAG,CAACmC;0BACnBpC,KAAK,CAACC,GAAG,CAACmC,cAAc,wBAAAC,WAAA,M,cAE7B/G,mBAAA,CAAwC,QAAxCgH,WAAwC,EAAR,GAAC,G;;QAIrC9F,YAAA,CAUkBkD,0BAAA;MAVD/B,KAAK,EAAC,KAAK;MAACgC,KAAK,EAAC;;MACtBG,OAAO,EAAAC,QAAA,CAMTC,KANgB,KACXA,KAAK,CAACC,GAAG,CAACsC,OAAO,I,cAA7BjH,mBAAA,CAKO;;QAJDD,KAAK,EAAC,WAAW;QAChB8C,OAAK,EAAAxB,MAAA,IAAEb,MAAA,CAAA0E,YAAY,CAACR,KAAK,CAACC,GAAG,CAAC1C,SAAS;QACvCkD,KAAK,EAAET,KAAK,CAACC,GAAG,CAACsC;0BACnBvC,KAAK,CAACC,GAAG,CAACsC,OAAO,wBAAAC,WAAA,M,cAEtBlH,mBAAA,CAAwC,QAAxCmH,WAAwC,EAAR,GAAC,G;;QAIrCjG,YAAA,CAUkBkD,0BAAA;MAVD/B,KAAK,EAAC,YAAY;MAACgC,KAAK,EAAC;;MAC7BG,OAAO,EAAAC,QAAA,CAMTC,KANgB,KACXA,KAAK,CAACC,GAAG,CAACyC,cAAc,I,cAApCpH,mBAAA,CAKO;;QAJDD,KAAK,EAAC,WAAW;QAChB8C,OAAK,EAAAxB,MAAA,IAAEb,MAAA,CAAA0E,YAAY,CAACR,KAAK,CAACC,GAAG,CAAC1C,SAAS;QACvCkD,KAAK,EAAET,KAAK,CAACC,GAAG,CAACyC;0BACnB1C,KAAK,CAACC,GAAG,CAACyC,cAAc,wBAAAC,WAAA,M,cAE7BrH,mBAAA,CAAwC,QAAxCsH,WAAwC,EAAR,GAAC,G;;QAIrCpG,YAAA,CAUkBkD,0BAAA;MAVD/B,KAAK,EAAC,OAAO;MAACgC,KAAK,EAAC;;MACxBG,OAAO,EAAAC,QAAA,CAMTC,KANgB,KACXA,KAAK,CAACC,GAAG,CAAC4C,SAAS,I,cAA/BvH,mBAAA,CAKO;;QAJDD,KAAK,EAAC,WAAW;QAChB8C,OAAK,EAAAxB,MAAA,IAAEb,MAAA,CAAA0E,YAAY,CAACR,KAAK,CAACC,GAAG,CAAC1C,SAAS;QACvCkD,KAAK,EAAET,KAAK,CAACC,GAAG,CAAC4C;0BACnB7C,KAAK,CAACC,GAAG,CAAC4C,SAAS,wBAAAC,WAAA,M,cAExBxH,mBAAA,CAAwC,QAAxCyH,WAAwC,EAAR,GAAC,G;;QAIrCvG,YAAA,CAUkBkD,0BAAA;MAVD/B,KAAK,EAAC,MAAM;MAACgC,KAAK,EAAC;;MACvBG,OAAO,EAAAC,QAAA,CAMTC,KANgB,KACXA,KAAK,CAACC,GAAG,CAAC+C,QAAQ,I,cAA9B1H,mBAAA,CAKO;;QAJDD,KAAK,EAAC,WAAW;QAChB8C,OAAK,EAAAxB,MAAA,IAAEb,MAAA,CAAA0E,YAAY,CAACR,KAAK,CAACC,GAAG,CAAC1C,SAAS;QACvCkD,KAAK,EAAET,KAAK,CAACC,GAAG,CAAC+C;0BACnBhD,KAAK,CAACC,GAAG,CAAC+C,QAAQ,wBAAAC,WAAA,M,cAEvB3H,mBAAA,CAAwC,QAAxC4H,WAAwC,EAAR,GAAC,G;;QAIrC1G,YAAA,CAUkBkD,0BAAA;MAVD/B,KAAK,EAAC,MAAM;MAACgC,KAAK,EAAC;;MACvBG,OAAO,EAAAC,QAAA,CAMTC,KANgB,KACXA,KAAK,CAACC,GAAG,CAACkD,QAAQ,I,cAA9B7H,mBAAA,CAKO;;QAJDD,KAAK,EAAC,WAAW;QAChB8C,OAAK,EAAAxB,MAAA,IAAEb,MAAA,CAAA0E,YAAY,CAACR,KAAK,CAACC,GAAG,CAAC1C,SAAS;QACvCkD,KAAK,EAAET,KAAK,CAACC,GAAG,CAACkD;0BACnBnD,KAAK,CAACC,GAAG,CAACkD,QAAQ,wBAAAC,WAAA,M,cAEvB9H,mBAAA,CAAwC,QAAxC+H,WAAwC,EAAR,GAAC,G;;QAIrC7G,YAAA,CAKkBkD,0BAAA;MALD/B,KAAK,EAAC,IAAI;MAACgC,KAAK,EAAC,KAAK;MAACC,KAAK,EAAC;;MACjCE,OAAO,EAAAC,QAAA,CACiEC,KAD1D,KACvBxD,YAAA,CAAiF0B,oBAAA;QAAtEM,IAAI,EAAC,SAAS;QAAC8E,IAAI,EAAC,OAAO;QAAEnF,OAAK,EAAAxB,MAAA,IAAEb,MAAA,CAAAyH,OAAO,CAACvD,KAAK,CAACC,GAAG;;0BAAG,MAAErB,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;wDACrEpC,YAAA,CAAkF0B,oBAAA;QAAvEM,IAAI,EAAC,QAAQ;QAAC8E,IAAI,EAAC,OAAO;QAAEnF,OAAK,EAAAxB,MAAA,IAAEb,MAAA,CAAA0H,SAAS,CAACxD,KAAK,CAACC,GAAG;;0BAAG,MAAErB,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;;;;;2EAlN/D9C,MAAA,CAAA2H,OAAO,E,GAuNpBjI,mBAAA,QAAW,EACXC,mBAAA,CAUM,OAVNiI,WAUM,GATJlH,YAAA,CAQEmH,wBAAA;IAPQ,cAAY,EAAE7H,MAAA,CAAA8H,WAAW;gEAAX9H,MAAA,CAAA8H,WAAW,GAAAjH,MAAA;IACzB,WAAS,EAAEb,MAAA,CAAA+H,QAAQ;6DAAR/H,MAAA,CAAA+H,QAAQ,GAAAlH,MAAA;IAC1B,YAAU,EAAE,aAAa;IACzBmH,KAAK,EAAEhI,MAAA,CAAAC,UAAU;IAClBgI,MAAM,EAAC,yCAAyC;IAC/CC,YAAW,EAAElI,MAAA,CAAAmI,gBAAgB;IAC7BC,eAAc,EAAEpI,MAAA,CAAAqI;wGAIrB3I,mBAAA,cAAiB,EACjBgB,YAAA,CAyNY4H,oBAAA;gBAxNDtI,MAAA,CAAA+C,aAAa;iEAAb/C,MAAA,CAAA+C,aAAa,GAAAlC,MAAA;IACrB8D,KAAK,EAAE3E,MAAA,CAAAuI,UAAU;IAClB1E,KAAK,EAAC,OAAO;IACZ2E,OAAK,EAAExI,MAAA,CAAAyI;;IA+MGC,MAAM,EAAAzE,QAAA,CACf,MAGO,CAHPtE,mBAAA,CAGO,QAHPgJ,YAGO,GAFLjI,YAAA,CAAwD0B,oBAAA;MAA5CC,OAAK,EAAAS,MAAA,SAAAA,MAAA,OAAAjC,MAAA,IAAEb,MAAA,CAAA+C,aAAa;;wBAAU,MAAED,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;QAC5CpC,YAAA,CAA4E0B,oBAAA;MAAjEM,IAAI,EAAC,SAAS;MAAEL,OAAK,EAAErC,MAAA,CAAA4I,QAAQ;MAAGjB,OAAO,EAAE3H,MAAA,CAAA6I;;wBAAQ,MAAE/F,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;;sBAhNpE,MA2MU,CA3MVpC,YAAA,CA2MUoI,kBAAA;MA3MAC,KAAK,EAAE/I,MAAA,CAAAgJ,QAAQ;MAAGC,KAAK,EAAEjJ,MAAA,CAAAkJ,SAAS;MAAEC,GAAG,EAAC,SAAS;MAAC,aAAW,EAAC;;wBACtE,MAEe,CAFfzI,YAAA,CAEe0I,uBAAA;QAFDvH,KAAK,EAAC,WAAW;QAACkC,IAAI,EAAC;;0BACnC,MAAgE,CAAhErD,YAAA,CAAgE2I,mBAAA;sBAA7CrJ,MAAA,CAAAgJ,QAAQ,CAACvH,SAAS;qEAAlBzB,MAAA,CAAAgJ,QAAQ,CAACvH,SAAS,GAAAZ,MAAA;UAAG8B,QAAQ,EAAE3C,MAAA,CAAAuI;;;UAGpD7H,YAAA,CAee0I,uBAAA;QAfDvH,KAAK,EAAC,eAAe;QAACkC,IAAI,EAAC;;0BACvC,MAaY,CAbZrD,YAAA,CAaYC,oBAAA;sBAZDX,MAAA,CAAAgJ,QAAQ,CAACM,aAAa;qEAAtBtJ,MAAA,CAAAgJ,QAAQ,CAACM,aAAa,GAAAzI,MAAA;UAC/BC,WAAW,EAAC,UAAU;UACtBC,UAAU,EAAV,EAAU;UACV,cAAY,EAAZ,EAAY;UACZ,sBAAoB,EAApB,EAAoB;UACnB,iBAAe,EAAE,KAAK;UACtBG,QAAM,EAAEqI,IAAA,CAAAC;;4BAEP,MAAsC,E,kBADxChK,mBAAA,CAI0B8B,SAAA,QAAAC,WAAA,CAHPvB,MAAA,CAAAkC,oBAAoB,EAA9BC,MAAM;iCADfT,YAAA,CAI0BC,oBAAA;cAFvBC,GAAG,EAAEO,MAAM,CAACL,KAAK;cACjBD,KAAK,EAAEM,MAAM,CAACN,KAAK;cACnBC,KAAK,EAAEK,MAAM,CAACL;;;;;;UAIrBpB,YAAA,CAEe0I,uBAAA;QAFDvH,KAAK,EAAC,aAAa;QAACkC,IAAI,EAAC;;0BACrC,MAAyE,CAAzErD,YAAA,CAAyE2I,mBAAA;sBAAtDrJ,MAAA,CAAAgJ,QAAQ,CAAC5E,OAAO;qEAAhBpE,MAAA,CAAAgJ,QAAQ,CAAC5E,OAAO,GAAAvD,MAAA;UAAEC,WAAW,EAAC;;;UAGnDJ,YAAA,CAEe0I,uBAAA;QAFDvH,KAAK,EAAC,IAAI;QAACkC,IAAI,EAAC;;0BAC5B,MAAgF,CAAhFrD,YAAA,CAAgF+I,0BAAA;sBAAtDzJ,MAAA,CAAAgJ,QAAQ,CAACU,SAAS;qEAAlB1J,MAAA,CAAAgJ,QAAQ,CAACU,SAAS,GAAA7I,MAAA;UAAG8I,SAAS,EAAE,CAAC;UAAE7I,WAAW,EAAC;;;UAG3EJ,YAAA,CAEe0I,uBAAA;QAFDvH,KAAK,EAAC,IAAI;QAACkC,IAAI,EAAC;;0BAC5B,MAA+E,CAA/ErD,YAAA,CAA+E+I,0BAAA;sBAArDzJ,MAAA,CAAAgJ,QAAQ,CAACY,QAAQ;qEAAjB5J,MAAA,CAAAgJ,QAAQ,CAACY,QAAQ,GAAA/I,MAAA;UAAG8I,SAAS,EAAE,CAAC;UAAE7I,WAAW,EAAC;;;UAG1EpB,mBAAA,YAAe,EACfgB,YAAA,CAAqDmJ,qBAAA;QAAzC,kBAAgB,EAAC;MAAM;0BAAC,MAAI/G,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;UAExCnD,mBAAA,CAqKM,OArKNmK,WAqKM,GApKJpK,mBAAA,YAAe,EACfC,mBAAA,CAQM,OARNoK,WAQM,G,4BAPJpK,mBAAA,CAAsB,eAAf,SAAO,qBACdA,mBAAA,CAKM,OALNqK,WAKM,GAJQhK,MAAA,CAAAgJ,QAAQ,CAACiB,KAAK,CAACC,MAAM,I,cAAjC1K,mBAAA,CAA0F,QAA1F2K,WAA0F,EAAApK,gBAAA,CAA/BC,MAAA,CAAAgJ,QAAQ,CAACiB,KAAK,CAACC,MAAM,qB,cAChF1K,mBAAA,CAAuC,QAAvC4K,WAAuC,EAAV,KAAG,IAChC1J,YAAA,CAAsE0B,oBAAA;QAA3DoF,IAAI,EAAC,OAAO;QAAEnF,OAAK,EAAAS,MAAA,SAAAA,MAAA,OAAAjC,MAAA,IAAEb,MAAA,CAAAqK,UAAU;;0BAAY,MAAIvH,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;UACzC9C,MAAA,CAAAgJ,QAAQ,CAACiB,KAAK,CAACC,MAAM,I,cAAtCxI,YAAA,CAA+GU,oBAAA;;QAAvEoF,IAAI,EAAC,OAAO;QAAC9E,IAAI,EAAC,QAAQ;QAAEL,OAAK,EAAAS,MAAA,SAAAA,MAAA,OAAAjC,MAAA,IAAEb,MAAA,CAAAsK,UAAU;;0BAAY,MAAExH,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;mDAIvGpD,mBAAA,gBAAmB,EACnBC,mBAAA,CAQM,OARN4K,WAQM,G,4BAPJ5K,mBAAA,CAA0B,eAAnB,aAAW,qBAClBA,mBAAA,CAKM,OALN6K,WAKM,GAJQxK,MAAA,CAAAgJ,QAAQ,CAACiB,KAAK,CAACQ,UAAU,I,cAArCjL,mBAAA,CAAkG,QAAlGkL,WAAkG,EAAA3K,gBAAA,CAAnCC,MAAA,CAAAgJ,QAAQ,CAACiB,KAAK,CAACQ,UAAU,qB,cACxFjL,mBAAA,CAAuC,QAAvCmL,WAAuC,EAAV,KAAG,IAChCjK,YAAA,CAA0E0B,oBAAA;QAA/DoF,IAAI,EAAC,OAAO;QAAEnF,OAAK,EAAAS,MAAA,SAAAA,MAAA,OAAAjC,MAAA,IAAEb,MAAA,CAAAqK,UAAU;;0BAAgB,MAAIvH,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;UAC7C9C,MAAA,CAAAgJ,QAAQ,CAACiB,KAAK,CAACQ,UAAU,I,cAA1C/I,YAAA,CAAuHU,oBAAA;;QAA3EoF,IAAI,EAAC,OAAO;QAAC9E,IAAI,EAAC,QAAQ;QAAEL,OAAK,EAAAS,MAAA,SAAAA,MAAA,OAAAjC,MAAA,IAAEb,MAAA,CAAAsK,UAAU;;0BAAgB,MAAExH,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;mDAI/GpD,mBAAA,uBAA0B,EAC1BC,mBAAA,CAQM,OARNiL,WAQM,G,4BAPJjL,mBAAA,CAAiC,eAA1B,oBAAkB,qBACzBA,mBAAA,CAKM,OALNkL,WAKM,GAJQ7K,MAAA,CAAAgJ,QAAQ,CAACiB,KAAK,CAACa,gBAAgB,I,cAA3CtL,mBAAA,CAA8G,QAA9GuL,WAA8G,EAAAhL,gBAAA,CAAzCC,MAAA,CAAAgJ,QAAQ,CAACiB,KAAK,CAACa,gBAAgB,qB,cACpGtL,mBAAA,CAAuC,QAAvCwL,WAAuC,EAAV,KAAG,IAChCtK,YAAA,CAAgF0B,oBAAA;QAArEoF,IAAI,EAAC,OAAO;QAAEnF,OAAK,EAAAS,MAAA,SAAAA,MAAA,OAAAjC,MAAA,IAAEb,MAAA,CAAAqK,UAAU;;0BAAsB,MAAIvH,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;UACnD9C,MAAA,CAAAgJ,QAAQ,CAACiB,KAAK,CAACa,gBAAgB,I,cAAhDpJ,YAAA,CAAmIU,oBAAA;;QAAjFoF,IAAI,EAAC,OAAO;QAAC9E,IAAI,EAAC,QAAQ;QAAEL,OAAK,EAAAS,MAAA,SAAAA,MAAA,OAAAjC,MAAA,IAAEb,MAAA,CAAAsK,UAAU;;0BAAsB,MAAExH,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;mDAI3HpD,mBAAA,wBAA2B,EAC3BC,mBAAA,CAQM,OARNsL,WAQM,G,4BAPJtL,mBAAA,CAAkC,eAA3B,qBAAmB,qBAC1BA,mBAAA,CAKM,OALNuL,WAKM,GAJQlL,MAAA,CAAAgJ,QAAQ,CAACiB,KAAK,CAACkB,iBAAiB,I,cAA5C3L,mBAAA,CAAgH,QAAhH4L,WAAgH,EAAArL,gBAAA,CAA1CC,MAAA,CAAAgJ,QAAQ,CAACiB,KAAK,CAACkB,iBAAiB,qB,cACtG3L,mBAAA,CAAuC,QAAvC6L,WAAuC,EAAV,KAAG,IAChC3K,YAAA,CAAiF0B,oBAAA;QAAtEoF,IAAI,EAAC,OAAO;QAAEnF,OAAK,EAAAS,MAAA,SAAAA,MAAA,OAAAjC,MAAA,IAAEb,MAAA,CAAAqK,UAAU;;0BAAuB,MAAIvH,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;UACpD9C,MAAA,CAAAgJ,QAAQ,CAACiB,KAAK,CAACkB,iBAAiB,I,cAAjDzJ,YAAA,CAAqIU,oBAAA;;QAAlFoF,IAAI,EAAC,OAAO;QAAC9E,IAAI,EAAC,QAAQ;QAAEL,OAAK,EAAAS,MAAA,SAAAA,MAAA,OAAAjC,MAAA,IAAEb,MAAA,CAAAsK,UAAU;;0BAAuB,MAAExH,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;mDAI7HpD,mBAAA,4BAA+B,EAC/BC,mBAAA,CAQM,OARN2L,WAQM,G,4BAPJ3L,mBAAA,CAAsC,eAA/B,yBAAuB,qBAC9BA,mBAAA,CAKM,OALN4L,WAKM,GAJQvL,MAAA,CAAAgJ,QAAQ,CAACiB,KAAK,CAACuB,qBAAqB,I,cAAhDhM,mBAAA,CAAwH,QAAxHiM,WAAwH,EAAA1L,gBAAA,CAA9CC,MAAA,CAAAgJ,QAAQ,CAACiB,KAAK,CAACuB,qBAAqB,qB,cAC9GhM,mBAAA,CAAuC,QAAvCkM,WAAuC,EAAV,KAAG,IAChChL,YAAA,CAAqF0B,oBAAA;QAA1EoF,IAAI,EAAC,OAAO;QAAEnF,OAAK,EAAAS,MAAA,SAAAA,MAAA,OAAAjC,MAAA,IAAEb,MAAA,CAAAqK,UAAU;;0BAA2B,MAAIvH,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;UACxD9C,MAAA,CAAAgJ,QAAQ,CAACiB,KAAK,CAACuB,qBAAqB,I,cAArD9J,YAAA,CAA6IU,oBAAA;;QAAtFoF,IAAI,EAAC,OAAO;QAAC9E,IAAI,EAAC,QAAQ;QAAEL,OAAK,EAAAS,MAAA,SAAAA,MAAA,OAAAjC,MAAA,IAAEb,MAAA,CAAAsK,UAAU;;0BAA2B,MAAExH,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;mDAIrIpD,mBAAA,yBAA4B,EAC5BC,mBAAA,CAQM,OARNgM,WAQM,G,4BAPJhM,mBAAA,CAAmC,eAA5B,sBAAoB,qBAC3BA,mBAAA,CAKM,OALNiM,WAKM,GAJQ5L,MAAA,CAAAgJ,QAAQ,CAACiB,KAAK,CAAC4B,kBAAkB,I,cAA7CrM,mBAAA,CAAkH,QAAlHsM,WAAkH,EAAA/L,gBAAA,CAA3CC,MAAA,CAAAgJ,QAAQ,CAACiB,KAAK,CAAC4B,kBAAkB,qB,cACxGrM,mBAAA,CAAuC,QAAvCuM,WAAuC,EAAV,KAAG,IAChCrL,YAAA,CAAkF0B,oBAAA;QAAvEoF,IAAI,EAAC,OAAO;QAAEnF,OAAK,EAAAS,MAAA,SAAAA,MAAA,OAAAjC,MAAA,IAAEb,MAAA,CAAAqK,UAAU;;0BAAwB,MAAIvH,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;UACrD9C,MAAA,CAAAgJ,QAAQ,CAACiB,KAAK,CAAC4B,kBAAkB,I,cAAlDnK,YAAA,CAAuIU,oBAAA;;QAAnFoF,IAAI,EAAC,OAAO;QAAC9E,IAAI,EAAC,QAAQ;QAAEL,OAAK,EAAAS,MAAA,SAAAA,MAAA,OAAAjC,MAAA,IAAEb,MAAA,CAAAsK,UAAU;;0BAAwB,MAAExH,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;mDAI/HpD,mBAAA,wBAA2B,EAC3BC,mBAAA,CAQM,OARNqM,WAQM,G,4BAPJrM,mBAAA,CAAkC,eAA3B,qBAAmB,qBAC1BA,mBAAA,CAKM,OALNsM,WAKM,GAJQjM,MAAA,CAAAgJ,QAAQ,CAACiB,KAAK,CAACiC,iBAAiB,I,cAA5C1M,mBAAA,CAAgH,QAAhH2M,WAAgH,EAAApM,gBAAA,CAA1CC,MAAA,CAAAgJ,QAAQ,CAACiB,KAAK,CAACiC,iBAAiB,qB,cACtG1M,mBAAA,CAAuC,QAAvC4M,WAAuC,EAAV,KAAG,IAChC1L,YAAA,CAAiF0B,oBAAA;QAAtEoF,IAAI,EAAC,OAAO;QAAEnF,OAAK,EAAAS,MAAA,SAAAA,MAAA,OAAAjC,MAAA,IAAEb,MAAA,CAAAqK,UAAU;;0BAAuB,MAAIvH,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;UACpD9C,MAAA,CAAAgJ,QAAQ,CAACiB,KAAK,CAACiC,iBAAiB,I,cAAjDxK,YAAA,CAAqIU,oBAAA;;QAAlFoF,IAAI,EAAC,OAAO;QAAC9E,IAAI,EAAC,QAAQ;QAAEL,OAAK,EAAAS,MAAA,SAAAA,MAAA,OAAAjC,MAAA,IAAEb,MAAA,CAAAsK,UAAU;;0BAAuB,MAAExH,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;mDAI7HpD,mBAAA,wBAA2B,EAC3BC,mBAAA,CAQM,OARN0M,WAQM,G,4BAPJ1M,mBAAA,CAAkC,eAA3B,qBAAmB,qBAC1BA,mBAAA,CAKM,OALN2M,WAKM,GAJQtM,MAAA,CAAAgJ,QAAQ,CAACiB,KAAK,CAACsC,iBAAiB,I,cAA5C/M,mBAAA,CAAgH,QAAhHgN,WAAgH,EAAAzM,gBAAA,CAA1CC,MAAA,CAAAgJ,QAAQ,CAACiB,KAAK,CAACsC,iBAAiB,qB,cACtG/M,mBAAA,CAAuC,QAAvCiN,WAAuC,EAAV,KAAG,IAChC/L,YAAA,CAAiF0B,oBAAA;QAAtEoF,IAAI,EAAC,OAAO;QAAEnF,OAAK,EAAAS,MAAA,SAAAA,MAAA,OAAAjC,MAAA,IAAEb,MAAA,CAAAqK,UAAU;;0BAAuB,MAAIvH,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;UACpD9C,MAAA,CAAAgJ,QAAQ,CAACiB,KAAK,CAACsC,iBAAiB,I,cAAjD7K,YAAA,CAAqIU,oBAAA;;QAAlFoF,IAAI,EAAC,OAAO;QAAC9E,IAAI,EAAC,QAAQ;QAAEL,OAAK,EAAAS,MAAA,SAAAA,MAAA,OAAAjC,MAAA,IAAEb,MAAA,CAAAsK,UAAU;;0BAAuB,MAAExH,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;mDAI7HpD,mBAAA,WAAc,EACdC,mBAAA,CAQM,OARN+M,WAQM,G,4BAPJ/M,mBAAA,CAAqB,eAAd,QAAM,qBACbA,mBAAA,CAKM,OALNgN,WAKM,GAJQ3M,MAAA,CAAAgJ,QAAQ,CAACiB,KAAK,CAAC2C,KAAK,I,cAAhCpN,mBAAA,CAAwF,QAAxFqN,WAAwF,EAAA9M,gBAAA,CAA9BC,MAAA,CAAAgJ,QAAQ,CAACiB,KAAK,CAAC2C,KAAK,qB,cAC9EpN,mBAAA,CAAuC,QAAvCsN,WAAuC,EAAV,KAAG,IAChCpM,YAAA,CAAqE0B,oBAAA;QAA1DoF,IAAI,EAAC,OAAO;QAAEnF,OAAK,EAAAS,MAAA,SAAAA,MAAA,OAAAjC,MAAA,IAAEb,MAAA,CAAAqK,UAAU;;0BAAW,MAAIvH,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;UACxC9C,MAAA,CAAAgJ,QAAQ,CAACiB,KAAK,CAAC2C,KAAK,I,cAArClL,YAAA,CAA6GU,oBAAA;;QAAtEoF,IAAI,EAAC,OAAO;QAAC9E,IAAI,EAAC,QAAQ;QAAEL,OAAK,EAAAS,MAAA,SAAAA,MAAA,OAAAjC,MAAA,IAAEb,MAAA,CAAAsK,UAAU;;0BAAW,MAAExH,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;mDAIrGpD,mBAAA,gBAAmB,EACnBC,mBAAA,CAQM,OARNoN,WAQM,G,4BAPJpN,mBAAA,CAA0B,eAAnB,aAAW,qBAClBA,mBAAA,CAKM,OALNqN,WAKM,GAJQhN,MAAA,CAAAgJ,QAAQ,CAACiB,KAAK,CAACgD,UAAU,I,cAArCzN,mBAAA,CAAkG,QAAlG0N,WAAkG,EAAAnN,gBAAA,CAAnCC,MAAA,CAAAgJ,QAAQ,CAACiB,KAAK,CAACgD,UAAU,qB,cACxFzN,mBAAA,CAAuC,QAAvC2N,WAAuC,EAAV,KAAG,IAChCzM,YAAA,CAA0E0B,oBAAA;QAA/DoF,IAAI,EAAC,OAAO;QAAEnF,OAAK,EAAAS,MAAA,SAAAA,MAAA,OAAAjC,MAAA,IAAEb,MAAA,CAAAqK,UAAU;;0BAAgB,MAAIvH,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;UAC7C9C,MAAA,CAAAgJ,QAAQ,CAACiB,KAAK,CAACgD,UAAU,I,cAA1CvL,YAAA,CAAuHU,oBAAA;;QAA3EoF,IAAI,EAAC,OAAO;QAAC9E,IAAI,EAAC,QAAQ;QAAEL,OAAK,EAAAS,MAAA,SAAAA,MAAA,OAAAjC,MAAA,IAAEb,MAAA,CAAAsK,UAAU;;0BAAgB,MAAExH,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;mDAI/GpD,mBAAA,SAAY,EACZC,mBAAA,CAQM,OARNyN,WAQM,G,4BAPJzN,mBAAA,CAAmB,eAAZ,MAAI,qBACXA,mBAAA,CAKM,OALN0N,WAKM,GAJQrN,MAAA,CAAAgJ,QAAQ,CAACiB,KAAK,CAACqD,GAAG,I,cAA9B9N,mBAAA,CAAoF,QAApF+N,WAAoF,EAAAxN,gBAAA,CAA5BC,MAAA,CAAAgJ,QAAQ,CAACiB,KAAK,CAACqD,GAAG,qB,cAC1E9N,mBAAA,CAAuC,QAAvCgO,WAAuC,EAAV,KAAG,IAChC9M,YAAA,CAAmE0B,oBAAA;QAAxDoF,IAAI,EAAC,OAAO;QAAEnF,OAAK,EAAAS,MAAA,SAAAA,MAAA,OAAAjC,MAAA,IAAEb,MAAA,CAAAqK,UAAU;;0BAAS,MAAIvH,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;UACtC9C,MAAA,CAAAgJ,QAAQ,CAACiB,KAAK,CAACqD,GAAG,I,cAAnC5L,YAAA,CAAyGU,oBAAA;;QAApEoF,IAAI,EAAC,OAAO;QAAC9E,IAAI,EAAC,QAAQ;QAAEL,OAAK,EAAAS,MAAA,SAAAA,MAAA,OAAAjC,MAAA,IAAEb,MAAA,CAAAsK,UAAU;;0BAAS,MAAExH,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;mDAIjGpD,mBAAA,gBAAmB,EACnBC,mBAAA,CAQM,OARN8N,WAQM,G,4BAPJ9N,mBAAA,CAA0B,eAAnB,aAAW,qBAClBA,mBAAA,CAKM,OALN+N,WAKM,GAJQ1N,MAAA,CAAAgJ,QAAQ,CAACiB,KAAK,CAAC0D,UAAU,I,cAArCnO,mBAAA,CAAkG,QAAlGoO,WAAkG,EAAA7N,gBAAA,CAAnCC,MAAA,CAAAgJ,QAAQ,CAACiB,KAAK,CAAC0D,UAAU,qB,cACxFnO,mBAAA,CAAuC,QAAvCqO,WAAuC,EAAV,KAAG,IAChCnN,YAAA,CAA0E0B,oBAAA;QAA/DoF,IAAI,EAAC,OAAO;QAAEnF,OAAK,EAAAS,MAAA,SAAAA,MAAA,OAAAjC,MAAA,IAAEb,MAAA,CAAAqK,UAAU;;0BAAgB,MAAIvH,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;UAC7C9C,MAAA,CAAAgJ,QAAQ,CAACiB,KAAK,CAAC0D,UAAU,I,cAA1CjM,YAAA,CAAuHU,oBAAA;;QAA3EoF,IAAI,EAAC,OAAO;QAAC9E,IAAI,EAAC,QAAQ;QAAEL,OAAK,EAAAS,MAAA,SAAAA,MAAA,OAAAjC,MAAA,IAAEb,MAAA,CAAAsK,UAAU;;0BAAgB,MAAExH,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;mDAI/GpD,mBAAA,WAAc,EACdC,mBAAA,CAQM,OARNmO,WAQM,G,4BAPJnO,mBAAA,CAAqB,eAAd,QAAM,qBACbA,mBAAA,CAKM,OALNoO,WAKM,GAJQ/N,MAAA,CAAAgJ,QAAQ,CAACiB,KAAK,CAAC+D,KAAK,I,cAAhCxO,mBAAA,CAAwF,QAAxFyO,WAAwF,EAAAlO,gBAAA,CAA9BC,MAAA,CAAAgJ,QAAQ,CAACiB,KAAK,CAAC+D,KAAK,qB,cAC9ExO,mBAAA,CAAuC,QAAvC0O,WAAuC,EAAV,KAAG,IAChCxN,YAAA,CAAqE0B,oBAAA;QAA1DoF,IAAI,EAAC,OAAO;QAAEnF,OAAK,EAAAS,MAAA,SAAAA,MAAA,OAAAjC,MAAA,IAAEb,MAAA,CAAAqK,UAAU;;0BAAW,MAAIvH,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;UACxC9C,MAAA,CAAAgJ,QAAQ,CAACiB,KAAK,CAAC+D,KAAK,I,cAArCtM,YAAA,CAA6GU,oBAAA;;QAAtEoF,IAAI,EAAC,OAAO;QAAC9E,IAAI,EAAC,QAAQ;QAAEL,OAAK,EAAAS,MAAA,SAAAA,MAAA,OAAAjC,MAAA,IAAEb,MAAA,CAAAsK,UAAU;;0BAAW,MAAExH,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;mDAIrGpD,mBAAA,UAAa,EACbC,mBAAA,CAQM,OARNwO,WAQM,G,4BAPJxO,mBAAA,CAAoB,eAAb,OAAK,qBACZA,mBAAA,CAKM,OALNyO,WAKM,GAJQpO,MAAA,CAAAgJ,QAAQ,CAACiB,KAAK,CAACoE,IAAI,I,cAA/B7O,mBAAA,CAAsF,QAAtF8O,YAAsF,EAAAvO,gBAAA,CAA7BC,MAAA,CAAAgJ,QAAQ,CAACiB,KAAK,CAACoE,IAAI,qB,cAC5E7O,mBAAA,CAAuC,QAAvC+O,YAAuC,EAAV,KAAG,IAChC7N,YAAA,CAAoE0B,oBAAA;QAAzDoF,IAAI,EAAC,OAAO;QAAEnF,OAAK,EAAAS,MAAA,SAAAA,MAAA,OAAAjC,MAAA,IAAEb,MAAA,CAAAqK,UAAU;;0BAAU,MAAIvH,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;UACvC9C,MAAA,CAAAgJ,QAAQ,CAACiB,KAAK,CAACoE,IAAI,I,cAApC3M,YAAA,CAA2GU,oBAAA;;QAArEoF,IAAI,EAAC,OAAO;QAAC9E,IAAI,EAAC,QAAQ;QAAEL,OAAK,EAAAS,MAAA,SAAAA,MAAA,OAAAjC,MAAA,IAAEb,MAAA,CAAAsK,UAAU;;0BAAU,MAAExH,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;mDAInGpD,mBAAA,UAAa,EACbC,mBAAA,CAQM,OARN6O,YAQM,G,4BAPJ7O,mBAAA,CAAoB,eAAb,OAAK,qBACZA,mBAAA,CAKM,OALN8O,YAKM,GAJQzO,MAAA,CAAAgJ,QAAQ,CAACiB,KAAK,CAACyE,IAAI,I,cAA/BlP,mBAAA,CAAsF,QAAtFmP,YAAsF,EAAA5O,gBAAA,CAA7BC,MAAA,CAAAgJ,QAAQ,CAACiB,KAAK,CAACyE,IAAI,qB,cAC5ElP,mBAAA,CAAuC,QAAvCoP,YAAuC,EAAV,KAAG,IAChClO,YAAA,CAAoE0B,oBAAA;QAAzDoF,IAAI,EAAC,OAAO;QAAEnF,OAAK,EAAAS,MAAA,SAAAA,MAAA,OAAAjC,MAAA,IAAEb,MAAA,CAAAqK,UAAU;;0BAAU,MAAIvH,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;UACvC9C,MAAA,CAAAgJ,QAAQ,CAACiB,KAAK,CAACyE,IAAI,I,cAApChN,YAAA,CAA2GU,oBAAA;;QAArEoF,IAAI,EAAC,OAAO;QAAC9E,IAAI,EAAC,QAAQ;QAAEL,OAAK,EAAAS,MAAA,SAAAA,MAAA,OAAAjC,MAAA,IAAEb,MAAA,CAAAsK,UAAU;;0BAAU,MAAExH,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, resolveDirective as _resolveDirective, openBlock as _openBlock, createElementBlock as _createElementBlock, withDirectives as _withDirectives, renderList as _renderList, Fragment as _Fragment, createBlock as _createBlock, createStaticVNode as _createStaticVNode } from \"vue\";\nconst _hoisted_1 = {\n  class: \"admin-dashboard\"\n};\nconst _hoisted_2 = {\n  class: \"admin-header\"\n};\nconst _hoisted_3 = {\n  class: \"header-right\"\n};\nconst _hoisted_4 = {\n  class: \"user-info\"\n};\nconst _hoisted_5 = {\n  class: \"admin-content\"\n};\nconst _hoisted_6 = {\n  class: \"admin-sidebar\"\n};\nconst _hoisted_7 = {\n  class: \"admin-main\"\n};\nconst _hoisted_8 = {\n  key: 0,\n  class: \"dashboard-content\"\n};\nconst _hoisted_9 = {\n  class: \"stats-cards\"\n};\nconst _hoisted_10 = {\n  class: \"stat-card\"\n};\nconst _hoisted_11 = {\n  class: \"stat-icon\"\n};\nconst _hoisted_12 = {\n  class: \"stat-info\"\n};\nconst _hoisted_13 = {\n  class: \"stat-number\"\n};\nconst _hoisted_14 = {\n  class: \"stat-card\"\n};\nconst _hoisted_15 = {\n  class: \"stat-icon\"\n};\nconst _hoisted_16 = {\n  class: \"stat-info\"\n};\nconst _hoisted_17 = {\n  class: \"stat-number\"\n};\nconst _hoisted_18 = {\n  class: \"subpopulation-stats\"\n};\nconst _hoisted_19 = {\n  class: \"stats-table\"\n};\nconst _hoisted_20 = {\n  class: \"stats-col-1\"\n};\nconst _hoisted_21 = {\n  class: \"stats-col-2\"\n};\nconst _hoisted_22 = {\n  class: \"stats-col-3\"\n};\nconst _hoisted_23 = {\n  class: \"stats-col-4\"\n};\nconst _hoisted_24 = {\n  class: \"category-stats\"\n};\nconst _hoisted_25 = {\n  class: \"stats-table\"\n};\nconst _hoisted_26 = {\n  class: \"stats-col-1\"\n};\nconst _hoisted_27 = {\n  class: \"stats-col-2\"\n};\nconst _hoisted_28 = {\n  class: \"stats-col-3\"\n};\nconst _hoisted_29 = {\n  class: \"stats-col-4\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_button = _resolveComponent(\"el-button\");\n  const _component_DataBoard = _resolveComponent(\"DataBoard\");\n  const _component_el_icon = _resolveComponent(\"el-icon\");\n  const _component_el_menu_item = _resolveComponent(\"el-menu-item\");\n  const _component_Document = _resolveComponent(\"Document\");\n  const _component_FolderOpened = _resolveComponent(\"FolderOpened\");\n  const _component_el_menu = _resolveComponent(\"el-menu\");\n  const _component_AdminFileManager = _resolveComponent(\"AdminFileManager\");\n  const _component_AdminDataManager = _resolveComponent(\"AdminDataManager\");\n  const _directive_loading = _resolveDirective(\"loading\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createCommentVNode(\" 顶部导航栏 \"), _createElementVNode(\"div\", _hoisted_2, [_cache[1] || (_cache[1] = _createElementVNode(\"div\", {\n    class: \"header-left\"\n  }, [_createElementVNode(\"h1\", null, \"基因数据管理系统\")], -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"span\", _hoisted_4, \"欢迎，\" + _toDisplayString($setup.userInfo.username), 1 /* TEXT */), _createVNode(_component_el_button, {\n    type: \"danger\",\n    size: \"small\",\n    onClick: $setup.handleLogout\n  }, {\n    default: _withCtx(() => _cache[0] || (_cache[0] = [_createTextVNode(\"退出登录\")])),\n    _: 1 /* STABLE */,\n    __: [0]\n  }, 8 /* PROPS */, [\"onClick\"])])]), _createCommentVNode(\" 主要内容区域 \"), _createElementVNode(\"div\", _hoisted_5, [_createCommentVNode(\" 侧边菜单 \"), _createElementVNode(\"div\", _hoisted_6, [_createVNode(_component_el_menu, {\n    \"default-active\": $setup.activeMenu,\n    class: \"sidebar-menu\",\n    onSelect: $setup.handleMenuSelect\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_menu_item, {\n      index: \"dashboard\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n        default: _withCtx(() => [_createVNode(_component_DataBoard)]),\n        _: 1 /* STABLE */\n      }), _cache[2] || (_cache[2] = _createElementVNode(\"span\", null, \"数据统计\", -1 /* CACHED */))]),\n      _: 1 /* STABLE */,\n      __: [2]\n    }), _createVNode(_component_el_menu_item, {\n      index: \"files\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n        default: _withCtx(() => [_createVNode(_component_Document)]),\n        _: 1 /* STABLE */\n      }), _cache[3] || (_cache[3] = _createElementVNode(\"span\", null, \"文件管理\", -1 /* CACHED */))]),\n      _: 1 /* STABLE */,\n      __: [3]\n    }), _createVNode(_component_el_menu_item, {\n      index: \"data-management\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n        default: _withCtx(() => [_createVNode(_component_FolderOpened)]),\n        _: 1 /* STABLE */\n      }), _cache[4] || (_cache[4] = _createElementVNode(\"span\", null, \"数据表格管理\", -1 /* CACHED */))]),\n      _: 1 /* STABLE */,\n      __: [4]\n    })]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"default-active\", \"onSelect\"])]), _createCommentVNode(\" 主内容区 \"), _createElementVNode(\"div\", _hoisted_7, [_createCommentVNode(\" 数据统计页面 \"), $setup.activeMenu === 'dashboard' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_8, [_cache[11] || (_cache[11] = _createElementVNode(\"h2\", null, \"数据统计\", -1 /* CACHED */)), _withDirectives((_openBlock(), _createElementBlock(\"div\", _hoisted_9, [_createElementVNode(\"div\", _hoisted_10, [_createElementVNode(\"div\", _hoisted_11, [_createVNode(_component_el_icon, null, {\n    default: _withCtx(() => [_createVNode(_component_Document)]),\n    _: 1 /* STABLE */\n  })]), _createElementVNode(\"div\", _hoisted_12, [_createElementVNode(\"div\", _hoisted_13, _toDisplayString($setup.statistics.total_files || 0), 1 /* TEXT */), _cache[5] || (_cache[5] = _createElementVNode(\"div\", {\n    class: \"stat-label\"\n  }, \"总文件数\", -1 /* CACHED */))])]), _createElementVNode(\"div\", _hoisted_14, [_createElementVNode(\"div\", _hoisted_15, [_createVNode(_component_el_icon, null, {\n    default: _withCtx(() => [_createVNode(_component_FolderOpened)]),\n    _: 1 /* STABLE */\n  })]), _createElementVNode(\"div\", _hoisted_16, [_createElementVNode(\"div\", _hoisted_17, _toDisplayString($setup.statistics.total_size_mb || 0) + \" MB\", 1 /* TEXT */), _cache[6] || (_cache[6] = _createElementVNode(\"div\", {\n    class: \"stat-label\"\n  }, \"总文件大小\", -1 /* CACHED */))])])])), [[_directive_loading, $setup.statsLoading]]), _createCommentVNode(\" 亚群统计 \"), _createElementVNode(\"div\", _hoisted_18, [_cache[8] || (_cache[8] = _createElementVNode(\"h3\", null, \"按亚群统计\", -1 /* CACHED */)), _withDirectives((_openBlock(), _createElementBlock(\"div\", _hoisted_19, [_cache[7] || (_cache[7] = _createStaticVNode(\"<div class=\\\"stats-header\\\" data-v-3808d382><span class=\\\"stats-col-1\\\" data-v-3808d382>亚群</span><span class=\\\"stats-col-2\\\" data-v-3808d382>Accession数量</span><span class=\\\"stats-col-3\\\" data-v-3808d382>文件数</span><span class=\\\"stats-col-4\\\" data-v-3808d382>文件总大小</span></div>\", 1)), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.subpopulationStats, stat => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: stat.subpopulation,\n      class: \"stats-row\"\n    }, [_createElementVNode(\"span\", _hoisted_20, _toDisplayString(stat.subpopulation), 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_21, _toDisplayString(stat.accession_count), 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_22, _toDisplayString(stat.file_count), 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_23, _toDisplayString($setup.formatFileSize(stat.total_size)), 1 /* TEXT */)]);\n  }), 128 /* KEYED_FRAGMENT */))])), [[_directive_loading, $setup.subpopStatsLoading]])]), _createCommentVNode(\" 分类统计 \"), _createElementVNode(\"div\", _hoisted_24, [_cache[10] || (_cache[10] = _createElementVNode(\"h3\", null, \"按类别统计\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_25, [_cache[9] || (_cache[9] = _createStaticVNode(\"<div class=\\\"stats-header\\\" data-v-3808d382><span class=\\\"stats-col-1\\\" data-v-3808d382>类别</span><span class=\\\"stats-col-2\\\" data-v-3808d382>Accession数量</span><span class=\\\"stats-col-3\\\" data-v-3808d382>文件数</span><span class=\\\"stats-col-4\\\" data-v-3808d382>文件总大小</span></div>\", 1)), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.statistics.category_stats, (stats, category) => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: category,\n      class: \"stats-row\"\n    }, [_createElementVNode(\"span\", _hoisted_26, _toDisplayString(category), 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_27, _toDisplayString($setup.formatAccessionCount(stats.accession_count)), 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_28, _toDisplayString(stats.file_count), 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_29, _toDisplayString($setup.formatFileSize(stats.total_size)), 1 /* TEXT */)]);\n  }), 128 /* KEYED_FRAGMENT */))])])])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 文件管理页面 \"), $setup.activeMenu === 'files' ? (_openBlock(), _createBlock(_component_AdminFileManager, {\n    key: 1,\n    onRefreshStats: $setup.loadStatistics\n  }, null, 8 /* PROPS */, [\"onRefreshStats\"])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 数据表格管理页面 \"), $setup.activeMenu === 'data-management' ? (_openBlock(), _createBlock(_component_AdminDataManager, {\n    key: 2\n  })) : _createCommentVNode(\"v-if\", true)])])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createCommentVNode", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_toDisplayString", "$setup", "userInfo", "username", "_createVNode", "_component_el_button", "type", "size", "onClick", "handleLogout", "_cache", "_hoisted_5", "_hoisted_6", "_component_el_menu", "activeMenu", "onSelect", "handleMenuSelect", "_component_el_menu_item", "index", "_component_el_icon", "_component_DataBoard", "_component_Document", "_component_FolderOpened", "_hoisted_7", "_hoisted_8", "_hoisted_9", "_hoisted_10", "_hoisted_11", "_hoisted_12", "_hoisted_13", "statistics", "total_files", "_hoisted_14", "_hoisted_15", "_hoisted_16", "_hoisted_17", "total_size_mb", "statsLoading", "_hoisted_18", "_hoisted_19", "_Fragment", "_renderList", "subpopulationStats", "stat", "key", "subpopulation", "_hoisted_20", "_hoisted_21", "accession_count", "_hoisted_22", "file_count", "_hoisted_23", "formatFileSize", "total_size", "subpopStatsLoading", "_hoisted_24", "_hoisted_25", "category_stats", "stats", "category", "_hoisted_26", "_hoisted_27", "formatAccessionCount", "_hoisted_28", "_hoisted_29", "_createBlock", "_component_AdminFileManager", "onRefreshStats", "loadStatistics", "_component_AdminDataManager"], "sources": ["D:\\gene_manage_system\\vue_project\\src\\views\\AdminDashboard.vue"], "sourcesContent": ["<template>\n  <div class=\"admin-dashboard\">\n    <!-- 顶部导航栏 -->\n    <div class=\"admin-header\">\n      <div class=\"header-left\">\n        <h1>基因数据管理系统</h1>\n      </div>\n      <div class=\"header-right\">\n        <span class=\"user-info\">欢迎，{{ userInfo.username }}</span>\n        <el-button type=\"danger\" size=\"small\" @click=\"handleLogout\">退出登录</el-button>\n      </div>\n    </div>\n    \n    <!-- 主要内容区域 -->\n    <div class=\"admin-content\">\n      <!-- 侧边菜单 -->\n      <div class=\"admin-sidebar\">\n        <el-menu\n          :default-active=\"activeMenu\"\n          class=\"sidebar-menu\"\n          @select=\"handleMenuSelect\"\n        >\n          <el-menu-item index=\"dashboard\">\n            <el-icon><DataBoard /></el-icon>\n            <span>数据统计</span>\n          </el-menu-item>\n          <el-menu-item index=\"files\">\n            <el-icon><Document /></el-icon>\n            <span>文件管理</span>\n          </el-menu-item>\n          <el-menu-item index=\"data-management\">\n            <el-icon><FolderOpened /></el-icon>\n            <span>数据表格管理</span>\n          </el-menu-item>\n        </el-menu>\n      </div>\n      \n      <!-- 主内容区 -->\n      <div class=\"admin-main\">\n        <!-- 数据统计页面 -->\n        <div v-if=\"activeMenu === 'dashboard'\" class=\"dashboard-content\">\n          <h2>数据统计</h2>\n          \n          <div class=\"stats-cards\" v-loading=\"statsLoading\">\n            <div class=\"stat-card\">\n              <div class=\"stat-icon\">\n                <el-icon><Document /></el-icon>\n              </div>\n              <div class=\"stat-info\">\n                <div class=\"stat-number\">{{ statistics.total_files || 0 }}</div>\n                <div class=\"stat-label\">总文件数</div>\n              </div>\n            </div>\n\n            <div class=\"stat-card\">\n              <div class=\"stat-icon\">\n                <el-icon><FolderOpened /></el-icon>\n              </div>\n              <div class=\"stat-info\">\n                <div class=\"stat-number\">{{ statistics.total_size_mb || 0 }} MB</div>\n                <div class=\"stat-label\">总文件大小</div>\n              </div>\n            </div>\n          </div>\n\n          <!-- 亚群统计 -->\n          <div class=\"subpopulation-stats\">\n            <h3>按亚群统计</h3>\n            <div class=\"stats-table\" v-loading=\"subpopStatsLoading\">\n              <div class=\"stats-header\">\n                <span class=\"stats-col-1\">亚群</span>\n                <span class=\"stats-col-2\">Accession数量</span>\n                <span class=\"stats-col-3\">文件数</span>\n                <span class=\"stats-col-4\">文件总大小</span>\n              </div>\n              <div v-for=\"stat in subpopulationStats\" :key=\"stat.subpopulation\" class=\"stats-row\">\n                <span class=\"stats-col-1\">{{ stat.subpopulation }}</span>\n                <span class=\"stats-col-2\">{{ stat.accession_count }}</span>\n                <span class=\"stats-col-3\">{{ stat.file_count }}</span>\n                <span class=\"stats-col-4\">{{ formatFileSize(stat.total_size) }}</span>\n              </div>\n            </div>\n          </div>\n\n          <!-- 分类统计 -->\n          <div class=\"category-stats\">\n            <h3>按类别统计</h3>\n            <div class=\"stats-table\">\n              <div class=\"stats-header\">\n                <span class=\"stats-col-1\">类别</span>\n                <span class=\"stats-col-2\">Accession数量</span>\n                <span class=\"stats-col-3\">文件数</span>\n                <span class=\"stats-col-4\">文件总大小</span>\n              </div>\n              <div v-for=\"(stats, category) in statistics.category_stats\" :key=\"category\" class=\"stats-row\">\n                <span class=\"stats-col-1\">{{ category }}</span>\n                <span class=\"stats-col-2\">{{ formatAccessionCount(stats.accession_count) }}</span>\n                <span class=\"stats-col-3\">{{ stats.file_count }}</span>\n                <span class=\"stats-col-4\">{{ formatFileSize(stats.total_size) }}</span>\n              </div>\n            </div>\n          </div>\n          \n\n        </div>\n\n        <!-- 文件管理页面 -->\n        <AdminFileManager v-if=\"activeMenu === 'files'\" @refresh-stats=\"loadStatistics\" />\n\n        <!-- 数据表格管理页面 -->\n        <AdminDataManager v-if=\"activeMenu === 'data-management'\" />\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { ref, reactive, onMounted } from 'vue'\nimport { useRouter } from 'vue-router'\nimport { ElMessage } from 'element-plus'\nimport { \n  DataBoard, Document, FolderOpened, Check, Close \n} from '@element-plus/icons-vue'\nimport axios from 'axios'\nimport AdminFileManager from './AdminFileManager.vue'\nimport AdminDataManager from './AdminDataManager.vue'\n\nexport default {\n  name: 'AdminDashboard',\n  components: {\n    AdminFileManager,\n    AdminDataManager,\n    DataBoard,\n    Document,\n    FolderOpened,\n    Check,\n    Close\n  },\n  setup() {\n    const router = useRouter()\n    const activeMenu = ref('dashboard')\n    const statsLoading = ref(false)\n    const subpopStatsLoading = ref(false)\n\n    const userInfo = reactive({\n      username: 'root'\n    })\n\n    const statistics = reactive({\n      total_files: 0,\n      total_size_mb: 0,\n      existing_files: 0,\n      missing_files: 0,\n      category_stats: {},\n      organism_stats: {}\n    })\n\n    const subpopulationStats = ref([])\n    \n    // 检查登录状态\n    const checkAuth = () => {\n      const token = localStorage.getItem('admin_token')\n      const user = localStorage.getItem('admin_user')\n      \n      if (!token || !user) {\n        router.push('/admin/login')\n        return false\n      }\n      \n      try {\n        const userData = JSON.parse(user)\n        userInfo.username = userData.username\n      } catch (e) {\n        router.push('/admin/login')\n        return false\n      }\n      \n      return true\n    }\n    \n    // 加载统计信息\n    const loadStatistics = async () => {\n      try {\n        statsLoading.value = true\n        const response = await axios.get('/admin/statistics/')\n\n        if (response.data.success) {\n          Object.assign(statistics, response.data.data)\n        }\n      } catch (error) {\n        console.error('加载统计信息失败:', error)\n        ElMessage.error('加载统计信息失败')\n      } finally {\n        statsLoading.value = false\n      }\n    }\n\n    // 加载亚群统计信息\n    const loadSubpopulationStats = async () => {\n      try {\n        subpopStatsLoading.value = true\n        const response = await axios.get('/admin/subpopulation-stats/')\n\n        if (response.data.success) {\n          subpopulationStats.value = response.data.data\n        }\n      } catch (error) {\n        console.error('加载亚群统计失败:', error)\n        ElMessage.error('加载亚群统计失败')\n      } finally {\n        subpopStatsLoading.value = false\n      }\n    }\n\n    // 获取亚群样式类名\n    const getSubPopulationClass = (subPopulation) => {\n      const classMap = {\n        'cA': 'sub-pop-ca',\n        'cB': 'sub-pop-cb',\n        'GJ': 'sub-pop-gj',\n        'XI': 'sub-pop-xi',\n        'WILD': 'sub-pop-wild',\n        'O.glaberrima': 'sub-pop-glaberrima',\n        '-': 'sub-pop-unknown'\n      }\n      return classMap[subPopulation] || 'sub-pop-default'\n    }\n\n    // 格式化文件大小\n    const formatFileSize = (bytes) => {\n      if (!bytes || bytes === 0) return '0 B'\n      const k = 1024\n      const sizes = ['B', 'KB', 'MB', 'GB']\n      const i = Math.floor(Math.log(bytes) / Math.log(k))\n      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]\n    }\n    \n    // 菜单选择\n    const handleMenuSelect = (index) => {\n      activeMenu.value = index\n    }\n    \n    // 退出登录\n    const handleLogout = () => {\n      localStorage.removeItem('admin_token')\n      localStorage.removeItem('admin_user')\n      ElMessage.success('已退出登录')\n      router.push('/admin/login')\n    }\n    \n    // 格式化Accession数量显示\n    const formatAccessionCount = (count) => {\n      return (count !== null && count !== undefined) ? count : '-'\n    }\n\n    onMounted(() => {\n      if (checkAuth()) {\n        loadStatistics()\n        loadSubpopulationStats()\n      }\n    })\n\n    return {\n      activeMenu,\n      userInfo,\n      statistics,\n      statsLoading,\n      subpopulationStats,\n      subpopStatsLoading,\n      handleMenuSelect,\n      handleLogout,\n      loadStatistics,\n      loadSubpopulationStats,\n      getSubPopulationClass,\n      formatFileSize,\n      formatAccessionCount\n    }\n  }\n}\n</script>\n\n<style scoped>\n.admin-dashboard {\n  height: 100vh;\n  display: flex;\n  flex-direction: column;\n}\n\n.admin-header {\n  height: 60px;\n  background: #409eff;\n  color: white;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 0 20px;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n}\n\n.header-left h1 {\n  margin: 0;\n  font-size: 20px;\n  font-weight: 500;\n}\n\n.header-right {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n}\n\n.user-info {\n  font-size: 14px;\n}\n\n.admin-content {\n  flex: 1;\n  display: flex;\n  overflow: hidden;\n}\n\n.admin-sidebar {\n  width: 200px;\n  background: #001529;\n  border-right: 1px solid #e6e6e6;\n}\n\n.sidebar-menu {\n  border: none;\n  background: transparent;\n}\n\n/* 菜单项样式 */\n.sidebar-menu .el-menu-item {\n  color: rgba(255, 255, 255, 0.65) !important;\n  background-color: transparent !important;\n}\n\n.sidebar-menu .el-menu-item:hover {\n  color: #fff !important;\n  background-color: #1890ff !important;\n}\n\n.sidebar-menu .el-menu-item.is-active {\n  color: #fff !important;\n  background-color: #1890ff !important;\n}\n\n.admin-main {\n  flex: 1;\n  padding: 20px;\n  overflow-y: auto;\n}\n\n.dashboard-content h2 {\n  margin-top: 0;\n  margin-bottom: 20px;\n  color: #333;\n}\n\n.stats-cards {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 20px;\n  margin-bottom: 30px;\n}\n\n.stat-card {\n  background: white;\n  padding: 20px;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n  display: flex;\n  align-items: center;\n  gap: 15px;\n}\n\n.stat-icon {\n  width: 50px;\n  height: 50px;\n  background: #409eff;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-size: 20px;\n}\n\n.stat-number {\n  font-size: 24px;\n  font-weight: bold;\n  color: #333;\n}\n\n.stat-label {\n  font-size: 14px;\n  color: #666;\n  margin-top: 5px;\n}\n\n.subpopulation-stats, .category-stats {\n  background: white;\n  padding: 20px;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n  margin-bottom: 20px;\n}\n\n.subpopulation-stats h3, .category-stats h3 {\n  margin-top: 0;\n  margin-bottom: 15px;\n  color: #333;\n}\n\n.stats-table {\n  border: 1px solid #e0e0e0;\n  border-radius: 4px;\n  overflow: hidden;\n}\n\n.stats-header {\n  display: flex;\n  background: #f5f5f5;\n  font-weight: bold;\n  color: #333;\n  border-bottom: 1px solid #e0e0e0;\n}\n\n.stats-row {\n  display: flex;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.stats-row:last-child {\n  border-bottom: none;\n}\n\n.stats-row:hover {\n  background: #f9f9f9;\n}\n\n/* 亚群统计的列宽 */\n.subpopulation-stats .stats-header,\n.subpopulation-stats .stats-row {\n  display: grid;\n  grid-template-columns: 1fr 1fr 1fr 1fr;\n  gap: 0;\n}\n\n.subpopulation-stats .stats-col-1,\n.subpopulation-stats .stats-col-2,\n.subpopulation-stats .stats-col-3,\n.subpopulation-stats .stats-col-4 {\n  padding: 12px 15px;\n  text-align: left;\n  font-weight: 500;\n}\n\n.subpopulation-stats .stats-col-1 {\n  color: #333;\n}\n\n.subpopulation-stats .stats-col-2,\n.subpopulation-stats .stats-col-3 {\n  color: #409eff;\n}\n\n.subpopulation-stats .stats-col-4 {\n  color: #67c23a;\n}\n\n/* 类别统计的列宽 */\n.category-stats .stats-header,\n.category-stats .stats-row {\n  display: grid;\n  grid-template-columns: 1fr 1fr 1fr 1fr;\n  gap: 0;\n}\n\n.category-stats .stats-col-1,\n.category-stats .stats-col-2,\n.category-stats .stats-col-3,\n.category-stats .stats-col-4 {\n  padding: 12px 15px;\n  text-align: left;\n  font-weight: 500;\n}\n\n.category-stats .stats-col-1 {\n  color: #333;\n}\n\n.category-stats .stats-col-2,\n.category-stats .stats-col-3 {\n  color: #409eff;\n}\n\n.category-stats .stats-col-4 {\n  color: #67c23a;\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAiB;;EAErBA,KAAK,EAAC;AAAc;;EAIlBA,KAAK,EAAC;AAAc;;EACjBA,KAAK,EAAC;AAAW;;EAMtBA,KAAK,EAAC;AAAe;;EAEnBA,KAAK,EAAC;AAAe;;EAsBrBA,KAAK,EAAC;AAAY;;;EAEkBA,KAAK,EAAC;;;EAGtCA,KAAK,EAAC;AAAa;;EACjBA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAW;;EAGjBA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAa;;EAKvBA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAW;;EAGjBA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAa;;EAOzBA,KAAK,EAAC;AAAqB;;EAEzBA,KAAK,EAAC;AAAa;;EAQdA,KAAK,EAAC;AAAa;;EACnBA,KAAK,EAAC;AAAa;;EACnBA,KAAK,EAAC;AAAa;;EACnBA,KAAK,EAAC;AAAa;;EAM1BA,KAAK,EAAC;AAAgB;;EAEpBA,KAAK,EAAC;AAAa;;EAQdA,KAAK,EAAC;AAAa;;EACnBA,KAAK,EAAC;AAAa;;EACnBA,KAAK,EAAC;AAAa;;EACnBA,KAAK,EAAC;AAAa;;;;;;;;;;;;uBAjGvCC,mBAAA,CAgHM,OAhHNC,UAgHM,GA/GJC,mBAAA,WAAc,EACdC,mBAAA,CAQM,OARNC,UAQM,G,0BAPJD,mBAAA,CAEM;IAFDJ,KAAK,EAAC;EAAa,IACtBI,mBAAA,CAAiB,YAAb,UAAQ,E,qBAEdA,mBAAA,CAGM,OAHNE,UAGM,GAFJF,mBAAA,CAAyD,QAAzDG,UAAyD,EAAjC,KAAG,GAAAC,gBAAA,CAAGC,MAAA,CAAAC,QAAQ,CAACC,QAAQ,kBAC/CC,YAAA,CAA4EC,oBAAA;IAAjEC,IAAI,EAAC,QAAQ;IAACC,IAAI,EAAC,OAAO;IAAEC,OAAK,EAAEP,MAAA,CAAAQ;;sBAAc,MAAIC,MAAA,QAAAA,MAAA,O,iBAAJ,MAAI,E;;;sCAIpEf,mBAAA,YAAe,EACfC,mBAAA,CAkGM,OAlGNe,UAkGM,GAjGJhB,mBAAA,UAAa,EACbC,mBAAA,CAmBM,OAnBNgB,UAmBM,GAlBJR,YAAA,CAiBUS,kBAAA;IAhBP,gBAAc,EAAEZ,MAAA,CAAAa,UAAU;IAC3BtB,KAAK,EAAC,cAAc;IACnBuB,QAAM,EAAEd,MAAA,CAAAe;;sBAET,MAGe,CAHfZ,YAAA,CAGea,uBAAA;MAHDC,KAAK,EAAC;IAAW;wBAC7B,MAAgC,CAAhCd,YAAA,CAAgCe,kBAAA;0BAAvB,MAAa,CAAbf,YAAA,CAAagB,oBAAA,E;;oCACtBxB,mBAAA,CAAiB,cAAX,MAAI,oB;;;QAEZQ,YAAA,CAGea,uBAAA;MAHDC,KAAK,EAAC;IAAO;wBACzB,MAA+B,CAA/Bd,YAAA,CAA+Be,kBAAA;0BAAtB,MAAY,CAAZf,YAAA,CAAYiB,mBAAA,E;;oCACrBzB,mBAAA,CAAiB,cAAX,MAAI,oB;;;QAEZQ,YAAA,CAGea,uBAAA;MAHDC,KAAK,EAAC;IAAiB;wBACnC,MAAmC,CAAnCd,YAAA,CAAmCe,kBAAA;0BAA1B,MAAgB,CAAhBf,YAAA,CAAgBkB,uBAAA,E;;oCACzB1B,mBAAA,CAAmB,cAAb,QAAM,oB;;;;;uDAKlBD,mBAAA,UAAa,EACbC,mBAAA,CAyEM,OAzEN2B,UAyEM,GAxEJ5B,mBAAA,YAAe,EACJM,MAAA,CAAAa,UAAU,oB,cAArBrB,mBAAA,CAgEM,OAhEN+B,UAgEM,G,4BA/DJ5B,mBAAA,CAAa,YAAT,MAAI,qB,+BAERH,mBAAA,CAoBM,OApBNgC,UAoBM,GAnBJ7B,mBAAA,CAQM,OARN8B,WAQM,GAPJ9B,mBAAA,CAEM,OAFN+B,WAEM,GADJvB,YAAA,CAA+Be,kBAAA;sBAAtB,MAAY,CAAZf,YAAA,CAAYiB,mBAAA,E;;QAEvBzB,mBAAA,CAGM,OAHNgC,WAGM,GAFJhC,mBAAA,CAAgE,OAAhEiC,WAAgE,EAAA7B,gBAAA,CAApCC,MAAA,CAAA6B,UAAU,CAACC,WAAW,uB,0BAClDnC,mBAAA,CAAkC;IAA7BJ,KAAK,EAAC;EAAY,GAAC,MAAI,oB,KAIhCI,mBAAA,CAQM,OARNoC,WAQM,GAPJpC,mBAAA,CAEM,OAFNqC,WAEM,GADJ7B,YAAA,CAAmCe,kBAAA;sBAA1B,MAAgB,CAAhBf,YAAA,CAAgBkB,uBAAA,E;;QAE3B1B,mBAAA,CAGM,OAHNsC,WAGM,GAFJtC,mBAAA,CAAqE,OAArEuC,WAAqE,EAAAnC,gBAAA,CAAzCC,MAAA,CAAA6B,UAAU,CAACM,aAAa,SAAQ,KAAG,iB,0BAC/DxC,mBAAA,CAAmC;IAA9BJ,KAAK,EAAC;EAAY,GAAC,OAAK,oB,8BAjBCS,MAAA,CAAAoC,YAAY,E,GAsBhD1C,mBAAA,UAAa,EACbC,mBAAA,CAgBM,OAhBN0C,WAgBM,G,0BAfJ1C,mBAAA,CAAc,YAAV,OAAK,qB,+BACTH,mBAAA,CAaM,OAbN8C,WAaM,G,4VANJ9C,mBAAA,CAKM+C,SAAA,QAAAC,WAAA,CALcxC,MAAA,CAAAyC,kBAAkB,EAA1BC,IAAI;yBAAhBlD,mBAAA,CAKM;MALmCmD,GAAG,EAAED,IAAI,CAACE,aAAa;MAAErD,KAAK,EAAC;QACtEI,mBAAA,CAAyD,QAAzDkD,WAAyD,EAAA9C,gBAAA,CAA5B2C,IAAI,CAACE,aAAa,kBAC/CjD,mBAAA,CAA2D,QAA3DmD,WAA2D,EAAA/C,gBAAA,CAA9B2C,IAAI,CAACK,eAAe,kBACjDpD,mBAAA,CAAsD,QAAtDqD,WAAsD,EAAAjD,gBAAA,CAAzB2C,IAAI,CAACO,UAAU,kBAC5CtD,mBAAA,CAAsE,QAAtEuD,WAAsE,EAAAnD,gBAAA,CAAzCC,MAAA,CAAAmD,cAAc,CAACT,IAAI,CAACU,UAAU,kB;2DAX3BpD,MAAA,CAAAqD,kBAAkB,E,KAgBxD3D,mBAAA,UAAa,EACbC,mBAAA,CAgBM,OAhBN2D,WAgBM,G,4BAfJ3D,mBAAA,CAAc,YAAV,OAAK,qBACTA,mBAAA,CAaM,OAbN4D,WAaM,G,4VANJ/D,mBAAA,CAKM+C,SAAA,QAAAC,WAAA,CAL2BxC,MAAA,CAAA6B,UAAU,CAAC2B,cAAc,GAA7CC,KAAK,EAAEC,QAAQ;yBAA5BlE,mBAAA,CAKM;MALuDmD,GAAG,EAAEe,QAAQ;MAAEnE,KAAK,EAAC;QAChFI,mBAAA,CAA+C,QAA/CgE,WAA+C,EAAA5D,gBAAA,CAAlB2D,QAAQ,kBACrC/D,mBAAA,CAAkF,QAAlFiE,WAAkF,EAAA7D,gBAAA,CAArDC,MAAA,CAAA6D,oBAAoB,CAACJ,KAAK,CAACV,eAAe,mBACvEpD,mBAAA,CAAuD,QAAvDmE,WAAuD,EAAA/D,gBAAA,CAA1B0D,KAAK,CAACR,UAAU,kBAC7CtD,mBAAA,CAAuE,QAAvEoE,WAAuE,EAAAhE,gBAAA,CAA1CC,MAAA,CAAAmD,cAAc,CAACM,KAAK,CAACL,UAAU,kB;6EAQpE1D,mBAAA,YAAe,EACSM,MAAA,CAAAa,UAAU,gB,cAAlCmD,YAAA,CAAkFC,2BAAA;;IAAjCC,cAAa,EAAElE,MAAA,CAAAmE;oFAEhEzE,mBAAA,cAAiB,EACOM,MAAA,CAAAa,UAAU,0B,cAAlCmD,YAAA,CAA4DI,2BAAA;IAAAzB,GAAA;EAAA,M", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport \"core-js/modules/es.set.difference.v2.js\";\nimport \"core-js/modules/es.set.intersection.v2.js\";\nimport \"core-js/modules/es.set.is-disjoint-from.v2.js\";\nimport \"core-js/modules/es.set.is-subset-of.v2.js\";\nimport \"core-js/modules/es.set.is-superset-of.v2.js\";\nimport \"core-js/modules/es.set.symmetric-difference.v2.js\";\nimport \"core-js/modules/es.set.union.v2.js\";\nimport { ref, reactive, onMounted, computed } from 'vue';\nimport { ElMessage, ElMessageBox } from 'element-plus';\nimport { Upload, Delete, Download, Refresh, Search, Warning, UploadFilled, FolderOpened } from '@element-plus/icons-vue';\nimport axios from 'axios';\nexport default {\n  name: 'AdminFileManager',\n  components: {\n    Upload,\n    Delete,\n    Download,\n    Refresh,\n    Search,\n    Warning,\n    UploadFilled,\n    FolderOpened\n  },\n  emits: ['refresh-stats'],\n  setup(props, {\n    emit\n  }) {\n    const loading = ref(false);\n    const uploading = ref(false);\n    const showUploadDialog = ref(false);\n    const showFolderUploadDialog = ref(false);\n    const uploadRef = ref(null);\n    const folderInputRef = ref(null);\n    const fileTableRef = ref(null);\n    const fileList = ref([]);\n    const folderFiles = ref([]);\n    const files = ref([]);\n    const selectedFiles = ref([]);\n    const currentPage = ref(1);\n    const pageSize = ref(20);\n    const total = ref(0);\n    const searchText = ref('');\n    const filterCategory = ref('');\n    const filterOrganism = ref('');\n    const organisms = ref([]);\n\n    // 文件类别选项\n    const categories = [{\n      value: 'genome',\n      label: '基因组序列'\n    }, {\n      value: 'transcriptome.all',\n      label: '转录组-All'\n    }, {\n      value: 'transcriptome.root',\n      label: '转录组-Root'\n    }, {\n      value: 'transcriptome.stem',\n      label: '转录组-Stem'\n    }, {\n      value: 'transcriptome.leaf',\n      label: '转录组-Leaf'\n    }, {\n      value: 'transcriptome.panicles',\n      label: '转录组-Panicles'\n    }, {\n      value: 'transcriptome.shoot',\n      label: '转录组-Shoot'\n    }, {\n      value: 'miRNA',\n      label: '微RNA'\n    }, {\n      value: 'tRNA',\n      label: '转运RNA'\n    }, {\n      value: 'rRNA',\n      label: '核糖体RNA'\n    }, {\n      value: 'codon',\n      label: '密码子'\n    }, {\n      value: 'centromere',\n      label: '着丝粒'\n    }, {\n      value: 'TEs',\n      label: '转座子'\n    }, {\n      value: 'annotation',\n      label: '基因注释'\n    }, {\n      value: 'coreBlocks',\n      label: '核心区块'\n    }, {\n      value: 'other',\n      label: '其他'\n    }];\n    const uploadUrl = computed(() => {\n      return '/admin/files/upload/';\n    });\n\n    // 获取类别标签\n    const getCategoryLabel = value => {\n      const category = categories.find(c => c.value === value);\n      return category ? category.label : value;\n    };\n\n    // 格式化文件大小\n    const formatFileSize = bytes => {\n      if (!bytes) return '0 B';\n      const k = 1024;\n      const sizes = ['B', 'KB', 'MB', 'GB'];\n      const i = Math.floor(Math.log(bytes) / Math.log(k));\n      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n    };\n\n    // 加载所有生物体列表\n    const loadOrganisms = async () => {\n      try {\n        // 获取所有文件的生物体列表（不带过滤条件）\n        const response = await axios.get('/admin/files/', {\n          params: {\n            page: 1,\n            page_size: 1000\n          } // 获取足够多的数据来提取生物体\n        });\n        if (response.data.success) {\n          const uniqueOrganisms = [...new Set(response.data.data.map(f => f.organism))];\n          organisms.value = uniqueOrganisms.filter(o => o && o !== 'unknown');\n        }\n      } catch (error) {\n        console.error('加载生物体列表失败:', error);\n      }\n    };\n\n    // 加载文件列表\n    const loadFiles = async () => {\n      try {\n        loading.value = true;\n        const params = {\n          page: currentPage.value,\n          page_size: pageSize.value,\n          search: searchText.value,\n          category: filterCategory.value,\n          organism: filterOrganism.value\n        };\n        const response = await axios.get('/admin/files/', {\n          params\n        });\n        if (response.data.success) {\n          files.value = response.data.data;\n          total.value = response.data.total;\n        }\n      } catch (error) {\n        console.error('加载文件列表失败:', error);\n        ElMessage.error('加载文件列表失败');\n      } finally {\n        loading.value = false;\n      }\n    };\n\n    // 搜索处理\n    const handleSearch = () => {\n      currentPage.value = 1;\n      loadFiles();\n    };\n\n    // 选择变化处理\n    const handleSelectionChange = selection => {\n      selectedFiles.value = selection || [];\n    };\n\n    // 下载文件\n    const handleDownload = file => {\n      const downloadUrl = `/gd/api/manual_download/${file.name}`;\n      window.open(downloadUrl, '_blank');\n    };\n\n    // 删除单个文件\n    const handleDelete = async file => {\n      try {\n        await ElMessageBox.confirm(`确定要删除文件 \"${file.name}\" 吗？此操作不可恢复。`, '确认删除', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        });\n        const response = await axios.delete(`/admin/files/${file.id}/delete/`);\n        if (response.data.success) {\n          ElMessage.success(response.data.message);\n          loadFiles();\n          emit('refresh-stats');\n        } else {\n          ElMessage.error(response.data.message);\n        }\n      } catch (error) {\n        if (error !== 'cancel') {\n          console.error('删除文件失败:', error);\n          ElMessage.error('删除文件失败');\n        }\n      }\n    };\n\n    // 批量删除\n    const handleBatchDelete = async () => {\n      if (!selectedFiles.value || selectedFiles.value.length === 0) {\n        ElMessage.warning('请选择要删除的文件');\n        return;\n      }\n      try {\n        await ElMessageBox.confirm(`确定要删除选中的 ${selectedFiles.value.length} 个文件吗？此操作不可恢复。`, '确认批量删除', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        });\n        const fileIds = selectedFiles.value.map(f => f.id);\n        const response = await axios.post('/admin/files/batch-delete/', {\n          file_ids: fileIds\n        });\n        if (response.data.success) {\n          ElMessage.success(response.data.message);\n          selectedFiles.value = [];\n          // 清除表格选择状态\n          if (fileTableRef.value) {\n            fileTableRef.value.clearSelection();\n          }\n          loadFiles();\n          emit('refresh-stats');\n        } else {\n          ElMessage.error(response.data.message);\n        }\n      } catch (error) {\n        if (error !== 'cancel') {\n          console.error('批量删除失败:', error);\n          ElMessage.error('批量删除失败');\n        }\n      }\n    };\n\n    // 批量下载\n    const handleBatchDownload = async () => {\n      if (!selectedFiles.value || selectedFiles.value.length === 0) {\n        ElMessage.warning('请选择要下载的文件');\n        return;\n      }\n      try {\n        const fileIds = selectedFiles.value.map(f => f.id);\n        const response = await axios.post('/admin/files/batch-download/', {\n          file_ids: fileIds\n        }, {\n          responseType: 'blob'\n        });\n\n        // 创建下载链接\n        const url = window.URL.createObjectURL(new Blob([response.data]));\n        const link = document.createElement('a');\n        link.href = url;\n\n        // 从响应头获取文件名，如果没有则使用默认名称\n        const contentDisposition = response.headers['content-disposition'];\n        let filename = 'batch_download.zip';\n        if (contentDisposition) {\n          const filenameMatch = contentDisposition.match(/filename=\"(.+)\"/);\n          if (filenameMatch) {\n            filename = filenameMatch[1];\n          }\n        }\n        link.download = filename;\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        window.URL.revokeObjectURL(url);\n        ElMessage.success(`成功下载 ${selectedFiles.value.length} 个文件`);\n        selectedFiles.value = [];\n        // 清除表格选择状态\n        if (fileTableRef.value) {\n          fileTableRef.value.clearSelection();\n        }\n      } catch (error) {\n        console.error('批量下载失败:', error);\n        ElMessage.error('批量下载失败');\n      }\n    };\n\n    // 重新扫描\n    const handleRescan = async () => {\n      try {\n        loading.value = true;\n        const response = await axios.post('/admin/rescan/');\n        if (response.data.success) {\n          ElMessage.success(response.data.message);\n          loadFiles();\n          emit('refresh-stats');\n        } else {\n          ElMessage.error(response.data.message);\n        }\n      } catch (error) {\n        console.error('重新扫描失败:', error);\n        ElMessage.error('重新扫描失败');\n      } finally {\n        loading.value = false;\n      }\n    };\n\n    // 文件变化处理\n    const handleFileChange = (file, uploadFileList) => {\n      fileList.value = uploadFileList;\n      console.log('文件列表更新:', fileList.value);\n    };\n\n    // 获取CSRF token\n    const getCsrfToken = () => {\n      const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]');\n      return csrfToken ? csrfToken.value : '';\n    };\n\n    // 上传处理\n    const handleUpload = async () => {\n      console.log('开始上传，文件列表:', fileList.value);\n      if (!fileList.value || fileList.value.length === 0) {\n        ElMessage.warning('请选择要上传的文件');\n        return;\n      }\n      try {\n        uploading.value = true;\n\n        // 逐个上传文件\n        for (const file of fileList.value) {\n          const formData = new FormData();\n          formData.append('file', file.raw);\n          const headers = {\n            'Content-Type': 'multipart/form-data'\n          };\n\n          // 添加CSRF token（如果存在）\n          const csrfToken = getCsrfToken();\n          if (csrfToken) {\n            headers['X-CSRFToken'] = csrfToken;\n          }\n          console.log('上传文件:', file.name);\n          await axios.post('/admin/files/upload/', formData, {\n            headers\n          });\n        }\n        ElMessage.success('所有文件上传成功');\n        showUploadDialog.value = false;\n        resetUpload();\n        loadFiles();\n        emit('refresh-stats');\n      } catch (error) {\n        console.error('上传失败:', error);\n        if (error.response && error.response.data && error.response.data.message) {\n          ElMessage.error(error.response.data.message);\n        } else {\n          ElMessage.error('上传失败，请检查网络连接');\n        }\n      } finally {\n        uploading.value = false;\n      }\n    };\n\n    // 上传成功处理\n    const handleUploadSuccess = (response, file) => {\n      ElMessage.success(`文件 ${file.name} 上传成功`);\n    };\n\n    // 上传失败处理\n    const handleUploadError = (error, file) => {\n      ElMessage.error(`文件 ${file.name} 上传失败`);\n    };\n\n    // 选择文件夹\n    const selectFolder = () => {\n      if (folderInputRef.value) {\n        folderInputRef.value.click();\n      }\n    };\n\n    // 处理文件夹选择\n    const handleFolderSelect = event => {\n      const files = Array.from(event.target.files);\n      folderFiles.value = files;\n      console.log('选择的文件夹文件:', files);\n    };\n\n    // 文件夹上传处理\n    const handleFolderUpload = async () => {\n      if (folderFiles.value.length === 0) {\n        ElMessage.warning('请选择要上传的文件夹');\n        return;\n      }\n      try {\n        uploading.value = true;\n        let successCount = 0;\n        let failCount = 0;\n\n        // 逐个上传文件\n        for (const file of folderFiles.value) {\n          try {\n            const formData = new FormData();\n            formData.append('file', file);\n            const headers = {\n              'Content-Type': 'multipart/form-data'\n            };\n\n            // 添加CSRF token（如果存在）\n            const csrfToken = getCsrfToken();\n            if (csrfToken) {\n              headers['X-CSRFToken'] = csrfToken;\n            }\n            console.log('上传文件:', file.name);\n            await axios.post('/admin/files/upload/', formData, {\n              headers\n            });\n            successCount++;\n          } catch (error) {\n            console.error(`文件 ${file.name} 上传失败:`, error);\n            failCount++;\n          }\n        }\n        if (successCount > 0) {\n          ElMessage.success(`成功上传 ${successCount} 个文件${failCount > 0 ? `，${failCount} 个文件失败` : ''}`);\n          showFolderUploadDialog.value = false;\n          resetFolderUpload();\n          loadFiles();\n          emit('refresh-stats');\n        } else {\n          ElMessage.error('所有文件上传失败');\n        }\n      } catch (error) {\n        console.error('文件夹上传失败:', error);\n        ElMessage.error('文件夹上传失败');\n      } finally {\n        uploading.value = false;\n      }\n    };\n\n    // 重置文件夹上传\n    const resetFolderUpload = () => {\n      folderFiles.value = [];\n      if (folderInputRef.value) {\n        folderInputRef.value.value = '';\n      }\n    };\n\n    // 重置上传\n    const resetUpload = () => {\n      fileList.value = [];\n      if (uploadRef.value) {\n        uploadRef.value.clearFiles();\n      }\n    };\n    onMounted(() => {\n      loadFiles();\n      loadOrganisms();\n    });\n    return {\n      loading,\n      uploading,\n      showUploadDialog,\n      showFolderUploadDialog,\n      uploadRef,\n      folderInputRef,\n      fileTableRef,\n      fileList,\n      folderFiles,\n      files,\n      selectedFiles,\n      currentPage,\n      pageSize,\n      total,\n      searchText,\n      filterCategory,\n      filterOrganism,\n      organisms,\n      categories,\n      uploadUrl,\n      getCategoryLabel,\n      formatFileSize,\n      loadFiles,\n      loadOrganisms,\n      handleSearch,\n      handleSelectionChange,\n      handleDownload,\n      handleDelete,\n      handleBatchDelete,\n      handleBatchDownload,\n      handleRescan,\n      handleFileChange,\n      handleUpload,\n      handleUploadSuccess,\n      handleUploadError,\n      selectFolder,\n      handleFolderSelect,\n      handleFolderUpload,\n      resetUpload,\n      resetFolderUpload\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "reactive", "onMounted", "computed", "ElMessage", "ElMessageBox", "Upload", "Delete", "Download", "Refresh", "Search", "Warning", "UploadFilled", "FolderOpened", "axios", "name", "components", "emits", "setup", "props", "emit", "loading", "uploading", "showUploadDialog", "showFolderUploadDialog", "uploadRef", "folderInputRef", "fileTableRef", "fileList", "folderFiles", "files", "selectedFiles", "currentPage", "pageSize", "total", "searchText", "filterCategory", "filterOrganism", "organisms", "categories", "value", "label", "uploadUrl", "getCategoryLabel", "category", "find", "c", "formatFileSize", "bytes", "k", "sizes", "i", "Math", "floor", "log", "parseFloat", "pow", "toFixed", "loadOrganisms", "response", "get", "params", "page", "page_size", "data", "success", "uniqueOrganisms", "Set", "map", "f", "organism", "filter", "o", "error", "console", "loadFiles", "search", "handleSearch", "handleSelectionChange", "selection", "handleDownload", "file", "downloadUrl", "window", "open", "handleDelete", "confirm", "confirmButtonText", "cancelButtonText", "type", "delete", "id", "message", "handleBatchDelete", "length", "warning", "fileIds", "post", "file_ids", "clearSelection", "handleBatchDownload", "responseType", "url", "URL", "createObjectURL", "Blob", "link", "document", "createElement", "href", "contentDisposition", "headers", "filename", "filenameMatch", "match", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "handleRescan", "handleFileChange", "uploadFileList", "getCsrfToken", "csrfToken", "querySelector", "handleUpload", "formData", "FormData", "append", "raw", "resetUpload", "handleUploadSuccess", "handleUploadError", "selectFolder", "handleFolderSelect", "event", "Array", "from", "target", "handleFolderUpload", "successCount", "failCount", "resetFolderUpload", "clearFiles"], "sources": ["D:\\gene_manage_system\\vue_project\\src\\views\\AdminFileManager.vue"], "sourcesContent": ["<template>\n  <div class=\"file-manager\">\n    <div class=\"page-header\">\n      <h2>文件管理</h2>\n    </div>\n    \n    <!-- 操作工具栏 -->\n    <div class=\"toolbar\">\n      <div class=\"toolbar-left\">\n        <el-input\n          v-model=\"searchText\"\n          placeholder=\"搜索文件名\"\n          style=\"width: 200px; margin-right: 10px;\"\n          clearable\n          @input=\"handleSearch\"\n        >\n          <template #prefix>\n            <el-icon><Search /></el-icon>\n          </template>\n        </el-input>\n\n        <el-select\n          v-model=\"filterCategory\"\n          placeholder=\"选择类别\"\n          style=\"width: 150px; margin-right: 10px;\"\n          clearable\n          @change=\"loadFiles\"\n        >\n          <el-option label=\"全部类别\" value=\"\" />\n          <el-option\n            v-for=\"category in categories\"\n            :key=\"category.value\"\n            :label=\"category.label\"\n            :value=\"category.value\"\n          />\n        </el-select>\n\n        <el-select\n          v-model=\"filterOrganism\"\n          placeholder=\"选择生物体\"\n          style=\"width: 150px;\"\n          clearable\n          @change=\"loadFiles\"\n        >\n          <el-option label=\"全部生物体\" value=\"\" />\n          <el-option\n            v-for=\"organism in organisms\"\n            :key=\"organism\"\n            :label=\"organism\"\n            :value=\"organism\"\n          />\n        </el-select>\n      </div>\n\n      <div class=\"toolbar-right\">\n        <el-button type=\"primary\" @click=\"showUploadDialog = true\">\n          <el-icon><Upload /></el-icon>\n          上传文件\n        </el-button>\n        <el-button type=\"success\" @click=\"showFolderUploadDialog = true\">\n          <el-icon><FolderOpened /></el-icon>\n          上传文件夹\n        </el-button>\n        <el-button\n          type=\"danger\"\n          :disabled=\"!selectedFiles || selectedFiles.length === 0\"\n          @click=\"handleBatchDelete\"\n        >\n          <el-icon><Delete /></el-icon>\n          批量删除 ({{ selectedFiles ? selectedFiles.length : 0 }})\n        </el-button>\n        <el-button\n          type=\"primary\"\n          :disabled=\"!selectedFiles || selectedFiles.length === 0\"\n          @click=\"handleBatchDownload\"\n        >\n          <el-icon><Download /></el-icon>\n          批量下载 ({{ selectedFiles ? selectedFiles.length : 0 }})\n        </el-button>\n        <el-button type=\"success\" @click=\"handleRescan\">\n          <el-icon><Refresh /></el-icon>\n          重新扫描\n        </el-button>\n      </div>\n    </div>\n    \n    <!-- 文件列表表格 -->\n    <div class=\"file-table\">\n      <el-table\n        ref=\"fileTableRef\"\n        :data=\"files\"\n        v-loading=\"loading\"\n        @selection-change=\"handleSelectionChange\"\n        stripe\n        style=\"width: 100%\"\n      >\n        <el-table-column type=\"selection\" width=\"55\" />\n        \n        <el-table-column prop=\"name\" label=\"文件名\" min-width=\"200\">\n          <template #default=\"scope\">\n            <div class=\"file-name\">\n              <el-icon v-if=\"!scope.row.exists\" class=\"missing-icon\"><Warning /></el-icon>\n              {{ scope.row.name }}\n            </div>\n          </template>\n        </el-table-column>\n        \n        <el-table-column prop=\"organism\" label=\"生物体\" width=\"120\" />\n        \n        <el-table-column prop=\"category\" label=\"类别\" width=\"150\">\n          <template #default=\"scope\">\n            <el-tag size=\"small\">{{ getCategoryLabel(scope.row.category) }}</el-tag>\n          </template>\n        </el-table-column>\n        \n        <el-table-column prop=\"file_type\" label=\"文件类型\" width=\"100\" />\n        \n        <el-table-column prop=\"size\" label=\"文件大小\" width=\"120\">\n          <template #default=\"scope\">\n            {{ formatFileSize(scope.row.size) }}\n          </template>\n        </el-table-column>\n        \n        <el-table-column prop=\"created_at\" label=\"创建时间\" width=\"160\" />\n        \n        <el-table-column label=\"状态\" width=\"80\">\n          <template #default=\"scope\">\n            <el-tag :type=\"scope.row.exists ? 'success' : 'danger'\" size=\"small\">\n              {{ scope.row.exists ? '正常' : '缺失' }}\n            </el-tag>\n          </template>\n        </el-table-column>\n        \n        <el-table-column label=\"操作\" width=\"150\" fixed=\"right\">\n          <template #default=\"scope\">\n            <el-button \n              type=\"primary\" \n              size=\"small\" \n              :disabled=\"!scope.row.exists\"\n              @click=\"handleDownload(scope.row)\"\n            >\n              下载\n            </el-button>\n            <el-button \n              type=\"danger\" \n              size=\"small\"\n              @click=\"handleDelete(scope.row)\"\n            >\n              删除\n            </el-button>\n          </template>\n        </el-table-column>\n      </el-table>\n      \n      <!-- 分页 -->\n      <div class=\"pagination\">\n        <el-pagination\n          v-model:current-page=\"currentPage\"\n          v-model:page-size=\"pageSize\"\n          :page-sizes=\"[10, 20, 50, 100]\"\n          :total=\"total\"\n          layout=\"total, sizes, prev, pager, next, jumper\"\n          @size-change=\"loadFiles\"\n          @current-change=\"loadFiles\"\n        />\n      </div>\n    </div>\n    \n    <!-- 上传文件对话框 -->\n    <el-dialog\n      v-model=\"showUploadDialog\"\n      title=\"上传文件\"\n      width=\"500px\"\n      @close=\"resetUpload\"\n    >\n      <el-upload\n        ref=\"uploadRef\"\n        :action=\"uploadUrl\"\n        :auto-upload=\"false\"\n        :on-change=\"handleFileChange\"\n        :on-success=\"handleUploadSuccess\"\n        :on-error=\"handleUploadError\"\n        :file-list=\"fileList\"\n        drag\n        multiple\n      >\n        <el-icon class=\"el-icon--upload\"><upload-filled /></el-icon>\n        <div class=\"el-upload__text\">\n          将文件拖到此处，或<em>点击上传</em>\n        </div>\n        <template #tip>\n          <div class=\"el-upload__tip\">\n            支持多文件上传，系统会自动识别文件类型和分类\n          </div>\n        </template>\n      </el-upload>\n\n      <template #footer>\n        <span class=\"dialog-footer\">\n          <el-button @click=\"showUploadDialog = false\">取消</el-button>\n          <el-button\n            type=\"primary\"\n            :loading=\"uploading\"\n            @click=\"handleUpload\"\n          >\n            {{ uploading ? '上传中...' : '开始上传' }}\n          </el-button>\n        </span>\n      </template>\n    </el-dialog>\n\n    <!-- 上传文件夹对话框 -->\n    <el-dialog\n      v-model=\"showFolderUploadDialog\"\n      title=\"上传文件夹\"\n      width=\"500px\"\n      @close=\"resetFolderUpload\"\n    >\n      <div class=\"folder-upload-area\">\n        <input\n          ref=\"folderInputRef\"\n          type=\"file\"\n          webkitdirectory\n          multiple\n          style=\"display: none\"\n          @change=\"handleFolderSelect\"\n        />\n        <div\n          class=\"folder-drop-zone\"\n          @click=\"selectFolder\"\n        >\n          <el-icon class=\"folder-icon\"><FolderOpened /></el-icon>\n          <div class=\"folder-upload-text\">\n            点击选择文件夹\n          </div>\n          <div class=\"folder-upload-tip\">\n            将上传文件夹中的所有文件\n          </div>\n        </div>\n\n        <div v-if=\"folderFiles.length > 0\" class=\"folder-files-preview\">\n          <h4>将要上传的文件 ({{ folderFiles.length }} 个):</h4>\n          <div class=\"files-list\">\n            <div\n              v-for=\"file in folderFiles.slice(0, 10)\"\n              :key=\"file.name\"\n              class=\"file-item\"\n            >\n              {{ file.webkitRelativePath || file.name }}\n            </div>\n            <div v-if=\"folderFiles.length > 10\" class=\"more-files\">\n              ... 还有 {{ folderFiles.length - 10 }} 个文件\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <template #footer>\n        <span class=\"dialog-footer\">\n          <el-button @click=\"showFolderUploadDialog = false\">取消</el-button>\n          <el-button\n            type=\"primary\"\n            :loading=\"uploading\"\n            :disabled=\"folderFiles.length === 0\"\n            @click=\"handleFolderUpload\"\n          >\n            {{ uploading ? '上传中...' : `开始上传 (${folderFiles.length} 个文件)` }}\n          </el-button>\n        </span>\n      </template>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { ref, reactive, onMounted, computed } from 'vue'\nimport { ElMessage, ElMessageBox } from 'element-plus'\nimport {\n  Upload, Delete, Download, Refresh, Search, Warning, UploadFilled, FolderOpened\n} from '@element-plus/icons-vue'\nimport axios from 'axios'\n\nexport default {\n  name: 'AdminFileManager',\n  components: {\n    Upload,\n    Delete,\n    Download,\n    Refresh,\n    Search,\n    Warning,\n    UploadFilled,\n    FolderOpened\n  },\n  emits: ['refresh-stats'],\n  setup(props, { emit }) {\n    const loading = ref(false)\n    const uploading = ref(false)\n    const showUploadDialog = ref(false)\n    const showFolderUploadDialog = ref(false)\n    const uploadRef = ref(null)\n    const folderInputRef = ref(null)\n    const fileTableRef = ref(null)\n    const fileList = ref([])\n    const folderFiles = ref([])\n\n    const files = ref([])\n    const selectedFiles = ref([])\n    const currentPage = ref(1)\n    const pageSize = ref(20)\n    const total = ref(0)\n    \n    const searchText = ref('')\n    const filterCategory = ref('')\n    const filterOrganism = ref('')\n    \n    const organisms = ref([])\n    \n    // 文件类别选项\n    const categories = [\n      { value: 'genome', label: '基因组序列' },\n      { value: 'transcriptome.all', label: '转录组-All' },\n      { value: 'transcriptome.root', label: '转录组-Root' },\n      { value: 'transcriptome.stem', label: '转录组-Stem' },\n      { value: 'transcriptome.leaf', label: '转录组-Leaf' },\n      { value: 'transcriptome.panicles', label: '转录组-Panicles' },\n      { value: 'transcriptome.shoot', label: '转录组-Shoot' },\n      { value: 'miRNA', label: '微RNA' },\n      { value: 'tRNA', label: '转运RNA' },\n      { value: 'rRNA', label: '核糖体RNA' },\n      { value: 'codon', label: '密码子' },\n      { value: 'centromere', label: '着丝粒' },\n      { value: 'TEs', label: '转座子' },\n      { value: 'annotation', label: '基因注释' },\n      { value: 'coreBlocks', label: '核心区块' },\n      { value: 'other', label: '其他' }\n    ]\n    \n    const uploadUrl = computed(() => {\n      return '/admin/files/upload/'\n    })\n    \n    // 获取类别标签\n    const getCategoryLabel = (value) => {\n      const category = categories.find(c => c.value === value)\n      return category ? category.label : value\n    }\n    \n    // 格式化文件大小\n    const formatFileSize = (bytes) => {\n      if (!bytes) return '0 B'\n      const k = 1024\n      const sizes = ['B', 'KB', 'MB', 'GB']\n      const i = Math.floor(Math.log(bytes) / Math.log(k))\n      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]\n    }\n    \n    // 加载所有生物体列表\n    const loadOrganisms = async () => {\n      try {\n        // 获取所有文件的生物体列表（不带过滤条件）\n        const response = await axios.get('/admin/files/', {\n          params: { page: 1, page_size: 1000 } // 获取足够多的数据来提取生物体\n        })\n\n        if (response.data.success) {\n          const uniqueOrganisms = [...new Set(response.data.data.map(f => f.organism))]\n          organisms.value = uniqueOrganisms.filter(o => o && o !== 'unknown')\n        }\n      } catch (error) {\n        console.error('加载生物体列表失败:', error)\n      }\n    }\n\n    // 加载文件列表\n    const loadFiles = async () => {\n      try {\n        loading.value = true\n        const params = {\n          page: currentPage.value,\n          page_size: pageSize.value,\n          search: searchText.value,\n          category: filterCategory.value,\n          organism: filterOrganism.value\n        }\n\n        const response = await axios.get('/admin/files/', { params })\n\n        if (response.data.success) {\n          files.value = response.data.data\n          total.value = response.data.total\n        }\n      } catch (error) {\n        console.error('加载文件列表失败:', error)\n        ElMessage.error('加载文件列表失败')\n      } finally {\n        loading.value = false\n      }\n    }\n\n    // 搜索处理\n    const handleSearch = () => {\n      currentPage.value = 1\n      loadFiles()\n    }\n\n    // 选择变化处理\n    const handleSelectionChange = (selection) => {\n      selectedFiles.value = selection || []\n    }\n\n    // 下载文件\n    const handleDownload = (file) => {\n      const downloadUrl = `/gd/api/manual_download/${file.name}`\n      window.open(downloadUrl, '_blank')\n    }\n\n    // 删除单个文件\n    const handleDelete = async (file) => {\n      try {\n        await ElMessageBox.confirm(\n          `确定要删除文件 \"${file.name}\" 吗？此操作不可恢复。`,\n          '确认删除',\n          {\n            confirmButtonText: '确定',\n            cancelButtonText: '取消',\n            type: 'warning'\n          }\n        )\n\n        const response = await axios.delete(`/admin/files/${file.id}/delete/`)\n\n        if (response.data.success) {\n          ElMessage.success(response.data.message)\n          loadFiles()\n          emit('refresh-stats')\n        } else {\n          ElMessage.error(response.data.message)\n        }\n      } catch (error) {\n        if (error !== 'cancel') {\n          console.error('删除文件失败:', error)\n          ElMessage.error('删除文件失败')\n        }\n      }\n    }\n\n    // 批量删除\n    const handleBatchDelete = async () => {\n      if (!selectedFiles.value || selectedFiles.value.length === 0) {\n        ElMessage.warning('请选择要删除的文件')\n        return\n      }\n\n      try {\n        await ElMessageBox.confirm(\n          `确定要删除选中的 ${selectedFiles.value.length} 个文件吗？此操作不可恢复。`,\n          '确认批量删除',\n          {\n            confirmButtonText: '确定',\n            cancelButtonText: '取消',\n            type: 'warning'\n          }\n        )\n\n        const fileIds = selectedFiles.value.map(f => f.id)\n        const response = await axios.post('/admin/files/batch-delete/', {\n          file_ids: fileIds\n        })\n\n        if (response.data.success) {\n          ElMessage.success(response.data.message)\n          selectedFiles.value = []\n          // 清除表格选择状态\n          if (fileTableRef.value) {\n            fileTableRef.value.clearSelection()\n          }\n          loadFiles()\n          emit('refresh-stats')\n        } else {\n          ElMessage.error(response.data.message)\n        }\n      } catch (error) {\n        if (error !== 'cancel') {\n          console.error('批量删除失败:', error)\n          ElMessage.error('批量删除失败')\n        }\n      }\n    }\n\n    // 批量下载\n    const handleBatchDownload = async () => {\n      if (!selectedFiles.value || selectedFiles.value.length === 0) {\n        ElMessage.warning('请选择要下载的文件')\n        return\n      }\n\n      try {\n        const fileIds = selectedFiles.value.map(f => f.id)\n        const response = await axios.post('/admin/files/batch-download/', {\n          file_ids: fileIds\n        }, {\n          responseType: 'blob'\n        })\n\n        // 创建下载链接\n        const url = window.URL.createObjectURL(new Blob([response.data]))\n        const link = document.createElement('a')\n        link.href = url\n\n        // 从响应头获取文件名，如果没有则使用默认名称\n        const contentDisposition = response.headers['content-disposition']\n        let filename = 'batch_download.zip'\n        if (contentDisposition) {\n          const filenameMatch = contentDisposition.match(/filename=\"(.+)\"/)\n          if (filenameMatch) {\n            filename = filenameMatch[1]\n          }\n        }\n\n        link.download = filename\n        document.body.appendChild(link)\n        link.click()\n        document.body.removeChild(link)\n        window.URL.revokeObjectURL(url)\n\n        ElMessage.success(`成功下载 ${selectedFiles.value.length} 个文件`)\n        selectedFiles.value = []\n        // 清除表格选择状态\n        if (fileTableRef.value) {\n          fileTableRef.value.clearSelection()\n        }\n\n      } catch (error) {\n        console.error('批量下载失败:', error)\n        ElMessage.error('批量下载失败')\n      }\n    }\n\n    // 重新扫描\n    const handleRescan = async () => {\n      try {\n        loading.value = true\n        const response = await axios.post('/admin/rescan/')\n\n        if (response.data.success) {\n          ElMessage.success(response.data.message)\n          loadFiles()\n          emit('refresh-stats')\n        } else {\n          ElMessage.error(response.data.message)\n        }\n      } catch (error) {\n        console.error('重新扫描失败:', error)\n        ElMessage.error('重新扫描失败')\n      } finally {\n        loading.value = false\n      }\n    }\n\n    // 文件变化处理\n    const handleFileChange = (file, uploadFileList) => {\n      fileList.value = uploadFileList\n      console.log('文件列表更新:', fileList.value)\n    }\n\n    // 获取CSRF token\n    const getCsrfToken = () => {\n      const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]')\n      return csrfToken ? csrfToken.value : ''\n    }\n\n    // 上传处理\n    const handleUpload = async () => {\n      console.log('开始上传，文件列表:', fileList.value)\n\n      if (!fileList.value || fileList.value.length === 0) {\n        ElMessage.warning('请选择要上传的文件')\n        return\n      }\n\n      try {\n        uploading.value = true\n\n        // 逐个上传文件\n        for (const file of fileList.value) {\n          const formData = new FormData()\n          formData.append('file', file.raw)\n\n          const headers = {\n            'Content-Type': 'multipart/form-data'\n          }\n\n          // 添加CSRF token（如果存在）\n          const csrfToken = getCsrfToken()\n          if (csrfToken) {\n            headers['X-CSRFToken'] = csrfToken\n          }\n\n          console.log('上传文件:', file.name)\n          await axios.post('/admin/files/upload/', formData, { headers })\n        }\n\n        ElMessage.success('所有文件上传成功')\n        showUploadDialog.value = false\n        resetUpload()\n        loadFiles()\n        emit('refresh-stats')\n\n      } catch (error) {\n        console.error('上传失败:', error)\n        if (error.response && error.response.data && error.response.data.message) {\n          ElMessage.error(error.response.data.message)\n        } else {\n          ElMessage.error('上传失败，请检查网络连接')\n        }\n      } finally {\n        uploading.value = false\n      }\n    }\n\n    // 上传成功处理\n    const handleUploadSuccess = (response, file) => {\n      ElMessage.success(`文件 ${file.name} 上传成功`)\n    }\n\n    // 上传失败处理\n    const handleUploadError = (error, file) => {\n      ElMessage.error(`文件 ${file.name} 上传失败`)\n    }\n\n    // 选择文件夹\n    const selectFolder = () => {\n      if (folderInputRef.value) {\n        folderInputRef.value.click()\n      }\n    }\n\n    // 处理文件夹选择\n    const handleFolderSelect = (event) => {\n      const files = Array.from(event.target.files)\n      folderFiles.value = files\n      console.log('选择的文件夹文件:', files)\n    }\n\n    // 文件夹上传处理\n    const handleFolderUpload = async () => {\n      if (folderFiles.value.length === 0) {\n        ElMessage.warning('请选择要上传的文件夹')\n        return\n      }\n\n      try {\n        uploading.value = true\n        let successCount = 0\n        let failCount = 0\n\n        // 逐个上传文件\n        for (const file of folderFiles.value) {\n          try {\n            const formData = new FormData()\n            formData.append('file', file)\n\n            const headers = {\n              'Content-Type': 'multipart/form-data'\n            }\n\n            // 添加CSRF token（如果存在）\n            const csrfToken = getCsrfToken()\n            if (csrfToken) {\n              headers['X-CSRFToken'] = csrfToken\n            }\n\n            console.log('上传文件:', file.name)\n            await axios.post('/admin/files/upload/', formData, { headers })\n            successCount++\n          } catch (error) {\n            console.error(`文件 ${file.name} 上传失败:`, error)\n            failCount++\n          }\n        }\n\n        if (successCount > 0) {\n          ElMessage.success(`成功上传 ${successCount} 个文件${failCount > 0 ? `，${failCount} 个文件失败` : ''}`)\n          showFolderUploadDialog.value = false\n          resetFolderUpload()\n          loadFiles()\n          emit('refresh-stats')\n        } else {\n          ElMessage.error('所有文件上传失败')\n        }\n\n      } catch (error) {\n        console.error('文件夹上传失败:', error)\n        ElMessage.error('文件夹上传失败')\n      } finally {\n        uploading.value = false\n      }\n    }\n\n    // 重置文件夹上传\n    const resetFolderUpload = () => {\n      folderFiles.value = []\n      if (folderInputRef.value) {\n        folderInputRef.value.value = ''\n      }\n    }\n\n    // 重置上传\n    const resetUpload = () => {\n      fileList.value = []\n      if (uploadRef.value) {\n        uploadRef.value.clearFiles()\n      }\n    }\n\n    onMounted(() => {\n      loadFiles()\n      loadOrganisms()\n    })\n\n    return {\n      loading,\n      uploading,\n      showUploadDialog,\n      showFolderUploadDialog,\n      uploadRef,\n      folderInputRef,\n      fileTableRef,\n      fileList,\n      folderFiles,\n      files,\n      selectedFiles,\n      currentPage,\n      pageSize,\n      total,\n      searchText,\n      filterCategory,\n      filterOrganism,\n      organisms,\n      categories,\n      uploadUrl,\n      getCategoryLabel,\n      formatFileSize,\n      loadFiles,\n      loadOrganisms,\n      handleSearch,\n      handleSelectionChange,\n      handleDownload,\n      handleDelete,\n      handleBatchDelete,\n      handleBatchDownload,\n      handleRescan,\n      handleFileChange,\n      handleUpload,\n      handleUploadSuccess,\n      handleUploadError,\n      selectFolder,\n      handleFolderSelect,\n      handleFolderUpload,\n      resetUpload,\n      resetFolderUpload\n    }\n  }\n}\n</script>\n\n<style scoped>\n.file-manager h2 {\n  margin-top: 0;\n  margin-bottom: 20px;\n  color: #333;\n}\n\n.toolbar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n  padding: 15px;\n  background: white;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n}\n\n.toolbar-left {\n  display: flex;\n  gap: 10px;\n}\n\n.toolbar-right {\n  display: flex;\n  align-items: center;\n}\n\n.file-table {\n  background: white;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n  overflow: hidden;\n}\n\n.file-name {\n  display: flex;\n  align-items: center;\n  gap: 5px;\n}\n\n.missing-icon {\n  color: #f56c6c;\n}\n\n.pagination {\n  padding: 20px;\n  text-align: right;\n}\n\n.dialog-footer {\n  display: flex;\n  justify-content: flex-end;\n  gap: 10px;\n}\n\n/* 文件夹上传样式 */\n.folder-upload-area {\n  padding: 20px 0;\n}\n\n.folder-drop-zone {\n  border: 2px dashed #d9d9d9;\n  border-radius: 6px;\n  padding: 40px;\n  text-align: center;\n  cursor: pointer;\n  transition: border-color 0.3s;\n}\n\n.folder-drop-zone:hover {\n  border-color: #409eff;\n}\n\n.folder-icon {\n  font-size: 48px;\n  color: #c0c4cc;\n  margin-bottom: 16px;\n}\n\n.folder-upload-text {\n  font-size: 16px;\n  color: #606266;\n  margin-bottom: 8px;\n}\n\n.folder-upload-tip {\n  font-size: 14px;\n  color: #909399;\n}\n\n.folder-files-preview {\n  margin-top: 20px;\n  padding: 16px;\n  background: #f5f7fa;\n  border-radius: 4px;\n}\n\n.folder-files-preview h4 {\n  margin: 0 0 12px 0;\n  color: #303133;\n  font-size: 14px;\n}\n\n.files-list {\n  max-height: 200px;\n  overflow-y: auto;\n}\n\n.file-item {\n  padding: 4px 0;\n  font-size: 13px;\n  color: #606266;\n  border-bottom: 1px solid #ebeef5;\n}\n\n.file-item:last-child {\n  border-bottom: none;\n}\n\n.more-files {\n  padding: 8px 0;\n  font-size: 13px;\n  color: #909399;\n  font-style: italic;\n}\n\n/* 页面标题样式 */\n.page-header {\n  margin-bottom: 20px;\n}\n\n.page-header h2 {\n  margin: 0 0 10px 0;\n  color: #1a56db;\n  font-size: 24px;\n  font-weight: 600;\n}\n</style>\n"], "mappings": ";;;;;;;;;AAmRA,SAASA,GAAG,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,QAAO,QAAS,KAAI;AACvD,SAASC,SAAS,EAAEC,YAAW,QAAS,cAAa;AACrD,SACEC,MAAM,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,MAAM,EAAEC,OAAO,EAAEC,YAAY,EAAEC,YAAW,QACxE,yBAAwB;AAC/B,OAAOC,KAAI,MAAO,OAAM;AAExB,eAAe;EACbC,IAAI,EAAE,kBAAkB;EACxBC,UAAU,EAAE;IACVV,MAAM;IACNC,MAAM;IACNC,QAAQ;IACRC,OAAO;IACPC,MAAM;IACNC,OAAO;IACPC,YAAY;IACZC;EACF,CAAC;EACDI,KAAK,EAAE,CAAC,eAAe,CAAC;EACxBC,KAAKA,CAACC,KAAK,EAAE;IAAEC;EAAK,CAAC,EAAE;IACrB,MAAMC,OAAM,GAAIrB,GAAG,CAAC,KAAK;IACzB,MAAMsB,SAAQ,GAAItB,GAAG,CAAC,KAAK;IAC3B,MAAMuB,gBAAe,GAAIvB,GAAG,CAAC,KAAK;IAClC,MAAMwB,sBAAqB,GAAIxB,GAAG,CAAC,KAAK;IACxC,MAAMyB,SAAQ,GAAIzB,GAAG,CAAC,IAAI;IAC1B,MAAM0B,cAAa,GAAI1B,GAAG,CAAC,IAAI;IAC/B,MAAM2B,YAAW,GAAI3B,GAAG,CAAC,IAAI;IAC7B,MAAM4B,QAAO,GAAI5B,GAAG,CAAC,EAAE;IACvB,MAAM6B,WAAU,GAAI7B,GAAG,CAAC,EAAE;IAE1B,MAAM8B,KAAI,GAAI9B,GAAG,CAAC,EAAE;IACpB,MAAM+B,aAAY,GAAI/B,GAAG,CAAC,EAAE;IAC5B,MAAMgC,WAAU,GAAIhC,GAAG,CAAC,CAAC;IACzB,MAAMiC,QAAO,GAAIjC,GAAG,CAAC,EAAE;IACvB,MAAMkC,KAAI,GAAIlC,GAAG,CAAC,CAAC;IAEnB,MAAMmC,UAAS,GAAInC,GAAG,CAAC,EAAE;IACzB,MAAMoC,cAAa,GAAIpC,GAAG,CAAC,EAAE;IAC7B,MAAMqC,cAAa,GAAIrC,GAAG,CAAC,EAAE;IAE7B,MAAMsC,SAAQ,GAAItC,GAAG,CAAC,EAAE;;IAExB;IACA,MAAMuC,UAAS,GAAI,CACjB;MAAEC,KAAK,EAAE,QAAQ;MAAEC,KAAK,EAAE;IAAQ,CAAC,EACnC;MAAED,KAAK,EAAE,mBAAmB;MAAEC,KAAK,EAAE;IAAU,CAAC,EAChD;MAAED,KAAK,EAAE,oBAAoB;MAAEC,KAAK,EAAE;IAAW,CAAC,EAClD;MAAED,KAAK,EAAE,oBAAoB;MAAEC,KAAK,EAAE;IAAW,CAAC,EAClD;MAAED,KAAK,EAAE,oBAAoB;MAAEC,KAAK,EAAE;IAAW,CAAC,EAClD;MAAED,KAAK,EAAE,wBAAwB;MAAEC,KAAK,EAAE;IAAe,CAAC,EAC1D;MAAED,KAAK,EAAE,qBAAqB;MAAEC,KAAK,EAAE;IAAY,CAAC,EACpD;MAAED,KAAK,EAAE,OAAO;MAAEC,KAAK,EAAE;IAAO,CAAC,EACjC;MAAED,KAAK,EAAE,MAAM;MAAEC,KAAK,EAAE;IAAQ,CAAC,EACjC;MAAED,KAAK,EAAE,MAAM;MAAEC,KAAK,EAAE;IAAS,CAAC,EAClC;MAAED,KAAK,EAAE,OAAO;MAAEC,KAAK,EAAE;IAAM,CAAC,EAChC;MAAED,KAAK,EAAE,YAAY;MAAEC,KAAK,EAAE;IAAM,CAAC,EACrC;MAAED,KAAK,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAM,CAAC,EAC9B;MAAED,KAAK,EAAE,YAAY;MAAEC,KAAK,EAAE;IAAO,CAAC,EACtC;MAAED,KAAK,EAAE,YAAY;MAAEC,KAAK,EAAE;IAAO,CAAC,EACtC;MAAED,KAAK,EAAE,OAAO;MAAEC,KAAK,EAAE;IAAK,EAChC;IAEA,MAAMC,SAAQ,GAAIvC,QAAQ,CAAC,MAAM;MAC/B,OAAO,sBAAqB;IAC9B,CAAC;;IAED;IACA,MAAMwC,gBAAe,GAAKH,KAAK,IAAK;MAClC,MAAMI,QAAO,GAAIL,UAAU,CAACM,IAAI,CAACC,CAAA,IAAKA,CAAC,CAACN,KAAI,KAAMA,KAAK;MACvD,OAAOI,QAAO,GAAIA,QAAQ,CAACH,KAAI,GAAID,KAAI;IACzC;;IAEA;IACA,MAAMO,cAAa,GAAKC,KAAK,IAAK;MAChC,IAAI,CAACA,KAAK,EAAE,OAAO,KAAI;MACvB,MAAMC,CAAA,GAAI,IAAG;MACb,MAAMC,KAAI,GAAI,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI;MACpC,MAAMC,CAAA,GAAIC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,GAAG,CAACN,KAAK,IAAII,IAAI,CAACE,GAAG,CAACL,CAAC,CAAC;MAClD,OAAOM,UAAU,CAAC,CAACP,KAAI,GAAII,IAAI,CAACI,GAAG,CAACP,CAAC,EAAEE,CAAC,CAAC,EAAEM,OAAO,CAAC,CAAC,CAAC,IAAI,GAAE,GAAIP,KAAK,CAACC,CAAC;IACxE;;IAEA;IACA,MAAMO,aAAY,GAAI,MAAAA,CAAA,KAAY;MAChC,IAAI;QACF;QACA,MAAMC,QAAO,GAAI,MAAM7C,KAAK,CAAC8C,GAAG,CAAC,eAAe,EAAE;UAChDC,MAAM,EAAE;YAAEC,IAAI,EAAE,CAAC;YAAEC,SAAS,EAAE;UAAK,EAAE;QACvC,CAAC;QAED,IAAIJ,QAAQ,CAACK,IAAI,CAACC,OAAO,EAAE;UACzB,MAAMC,eAAc,GAAI,CAAC,GAAG,IAAIC,GAAG,CAACR,QAAQ,CAACK,IAAI,CAACA,IAAI,CAACI,GAAG,CAACC,CAAA,IAAKA,CAAC,CAACC,QAAQ,CAAC,CAAC;UAC5EhC,SAAS,CAACE,KAAI,GAAI0B,eAAe,CAACK,MAAM,CAACC,CAAA,IAAKA,CAAA,IAAKA,CAAA,KAAM,SAAS;QACpE;MACF,EAAE,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,YAAY,EAAEA,KAAK;MACnC;IACF;;IAEA;IACA,MAAME,SAAQ,GAAI,MAAAA,CAAA,KAAY;MAC5B,IAAI;QACFtD,OAAO,CAACmB,KAAI,GAAI,IAAG;QACnB,MAAMqB,MAAK,GAAI;UACbC,IAAI,EAAE9B,WAAW,CAACQ,KAAK;UACvBuB,SAAS,EAAE9B,QAAQ,CAACO,KAAK;UACzBoC,MAAM,EAAEzC,UAAU,CAACK,KAAK;UACxBI,QAAQ,EAAER,cAAc,CAACI,KAAK;UAC9B8B,QAAQ,EAAEjC,cAAc,CAACG;QAC3B;QAEA,MAAMmB,QAAO,GAAI,MAAM7C,KAAK,CAAC8C,GAAG,CAAC,eAAe,EAAE;UAAEC;QAAO,CAAC;QAE5D,IAAIF,QAAQ,CAACK,IAAI,CAACC,OAAO,EAAE;UACzBnC,KAAK,CAACU,KAAI,GAAImB,QAAQ,CAACK,IAAI,CAACA,IAAG;UAC/B9B,KAAK,CAACM,KAAI,GAAImB,QAAQ,CAACK,IAAI,CAAC9B,KAAI;QAClC;MACF,EAAE,OAAOuC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK;QAChCrE,SAAS,CAACqE,KAAK,CAAC,UAAU;MAC5B,UAAU;QACRpD,OAAO,CAACmB,KAAI,GAAI,KAAI;MACtB;IACF;;IAEA;IACA,MAAMqC,YAAW,GAAIA,CAAA,KAAM;MACzB7C,WAAW,CAACQ,KAAI,GAAI;MACpBmC,SAAS,CAAC;IACZ;;IAEA;IACA,MAAMG,qBAAoB,GAAKC,SAAS,IAAK;MAC3ChD,aAAa,CAACS,KAAI,GAAIuC,SAAQ,IAAK,EAAC;IACtC;;IAEA;IACA,MAAMC,cAAa,GAAKC,IAAI,IAAK;MAC/B,MAAMC,WAAU,GAAI,2BAA2BD,IAAI,CAAClE,IAAI,EAAC;MACzDoE,MAAM,CAACC,IAAI,CAACF,WAAW,EAAE,QAAQ;IACnC;;IAEA;IACA,MAAMG,YAAW,GAAI,MAAOJ,IAAI,IAAK;MACnC,IAAI;QACF,MAAM5E,YAAY,CAACiF,OAAO,CACxB,YAAYL,IAAI,CAAClE,IAAI,cAAc,EACnC,MAAM,EACN;UACEwE,iBAAiB,EAAE,IAAI;UACvBC,gBAAgB,EAAE,IAAI;UACtBC,IAAI,EAAE;QACR,CACF;QAEA,MAAM9B,QAAO,GAAI,MAAM7C,KAAK,CAAC4E,MAAM,CAAC,gBAAgBT,IAAI,CAACU,EAAE,UAAU;QAErE,IAAIhC,QAAQ,CAACK,IAAI,CAACC,OAAO,EAAE;UACzB7D,SAAS,CAAC6D,OAAO,CAACN,QAAQ,CAACK,IAAI,CAAC4B,OAAO;UACvCjB,SAAS,CAAC;UACVvD,IAAI,CAAC,eAAe;QACtB,OAAO;UACLhB,SAAS,CAACqE,KAAK,CAACd,QAAQ,CAACK,IAAI,CAAC4B,OAAO;QACvC;MACF,EAAE,OAAOnB,KAAK,EAAE;QACd,IAAIA,KAAI,KAAM,QAAQ,EAAE;UACtBC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK;UAC9BrE,SAAS,CAACqE,KAAK,CAAC,QAAQ;QAC1B;MACF;IACF;;IAEA;IACA,MAAMoB,iBAAgB,GAAI,MAAAA,CAAA,KAAY;MACpC,IAAI,CAAC9D,aAAa,CAACS,KAAI,IAAKT,aAAa,CAACS,KAAK,CAACsD,MAAK,KAAM,CAAC,EAAE;QAC5D1F,SAAS,CAAC2F,OAAO,CAAC,WAAW;QAC7B;MACF;MAEA,IAAI;QACF,MAAM1F,YAAY,CAACiF,OAAO,CACxB,YAAYvD,aAAa,CAACS,KAAK,CAACsD,MAAM,gBAAgB,EACtD,QAAQ,EACR;UACEP,iBAAiB,EAAE,IAAI;UACvBC,gBAAgB,EAAE,IAAI;UACtBC,IAAI,EAAE;QACR,CACF;QAEA,MAAMO,OAAM,GAAIjE,aAAa,CAACS,KAAK,CAAC4B,GAAG,CAACC,CAAA,IAAKA,CAAC,CAACsB,EAAE;QACjD,MAAMhC,QAAO,GAAI,MAAM7C,KAAK,CAACmF,IAAI,CAAC,4BAA4B,EAAE;UAC9DC,QAAQ,EAAEF;QACZ,CAAC;QAED,IAAIrC,QAAQ,CAACK,IAAI,CAACC,OAAO,EAAE;UACzB7D,SAAS,CAAC6D,OAAO,CAACN,QAAQ,CAACK,IAAI,CAAC4B,OAAO;UACvC7D,aAAa,CAACS,KAAI,GAAI,EAAC;UACvB;UACA,IAAIb,YAAY,CAACa,KAAK,EAAE;YACtBb,YAAY,CAACa,KAAK,CAAC2D,cAAc,CAAC;UACpC;UACAxB,SAAS,CAAC;UACVvD,IAAI,CAAC,eAAe;QACtB,OAAO;UACLhB,SAAS,CAACqE,KAAK,CAACd,QAAQ,CAACK,IAAI,CAAC4B,OAAO;QACvC;MACF,EAAE,OAAOnB,KAAK,EAAE;QACd,IAAIA,KAAI,KAAM,QAAQ,EAAE;UACtBC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK;UAC9BrE,SAAS,CAACqE,KAAK,CAAC,QAAQ;QAC1B;MACF;IACF;;IAEA;IACA,MAAM2B,mBAAkB,GAAI,MAAAA,CAAA,KAAY;MACtC,IAAI,CAACrE,aAAa,CAACS,KAAI,IAAKT,aAAa,CAACS,KAAK,CAACsD,MAAK,KAAM,CAAC,EAAE;QAC5D1F,SAAS,CAAC2F,OAAO,CAAC,WAAW;QAC7B;MACF;MAEA,IAAI;QACF,MAAMC,OAAM,GAAIjE,aAAa,CAACS,KAAK,CAAC4B,GAAG,CAACC,CAAA,IAAKA,CAAC,CAACsB,EAAE;QACjD,MAAMhC,QAAO,GAAI,MAAM7C,KAAK,CAACmF,IAAI,CAAC,8BAA8B,EAAE;UAChEC,QAAQ,EAAEF;QACZ,CAAC,EAAE;UACDK,YAAY,EAAE;QAChB,CAAC;;QAED;QACA,MAAMC,GAAE,GAAInB,MAAM,CAACoB,GAAG,CAACC,eAAe,CAAC,IAAIC,IAAI,CAAC,CAAC9C,QAAQ,CAACK,IAAI,CAAC,CAAC;QAChE,MAAM0C,IAAG,GAAIC,QAAQ,CAACC,aAAa,CAAC,GAAG;QACvCF,IAAI,CAACG,IAAG,GAAIP,GAAE;;QAEd;QACA,MAAMQ,kBAAiB,GAAInD,QAAQ,CAACoD,OAAO,CAAC,qBAAqB;QACjE,IAAIC,QAAO,GAAI,oBAAmB;QAClC,IAAIF,kBAAkB,EAAE;UACtB,MAAMG,aAAY,GAAIH,kBAAkB,CAACI,KAAK,CAAC,iBAAiB;UAChE,IAAID,aAAa,EAAE;YACjBD,QAAO,GAAIC,aAAa,CAAC,CAAC;UAC5B;QACF;QAEAP,IAAI,CAACS,QAAO,GAAIH,QAAO;QACvBL,QAAQ,CAACS,IAAI,CAACC,WAAW,CAACX,IAAI;QAC9BA,IAAI,CAACY,KAAK,CAAC;QACXX,QAAQ,CAACS,IAAI,CAACG,WAAW,CAACb,IAAI;QAC9BvB,MAAM,CAACoB,GAAG,CAACiB,eAAe,CAAClB,GAAG;QAE9BlG,SAAS,CAAC6D,OAAO,CAAC,QAAQlC,aAAa,CAACS,KAAK,CAACsD,MAAM,MAAM;QAC1D/D,aAAa,CAACS,KAAI,GAAI,EAAC;QACvB;QACA,IAAIb,YAAY,CAACa,KAAK,EAAE;UACtBb,YAAY,CAACa,KAAK,CAAC2D,cAAc,CAAC;QACpC;MAEF,EAAE,OAAO1B,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK;QAC9BrE,SAAS,CAACqE,KAAK,CAAC,QAAQ;MAC1B;IACF;;IAEA;IACA,MAAMgD,YAAW,GAAI,MAAAA,CAAA,KAAY;MAC/B,IAAI;QACFpG,OAAO,CAACmB,KAAI,GAAI,IAAG;QACnB,MAAMmB,QAAO,GAAI,MAAM7C,KAAK,CAACmF,IAAI,CAAC,gBAAgB;QAElD,IAAItC,QAAQ,CAACK,IAAI,CAACC,OAAO,EAAE;UACzB7D,SAAS,CAAC6D,OAAO,CAACN,QAAQ,CAACK,IAAI,CAAC4B,OAAO;UACvCjB,SAAS,CAAC;UACVvD,IAAI,CAAC,eAAe;QACtB,OAAO;UACLhB,SAAS,CAACqE,KAAK,CAACd,QAAQ,CAACK,IAAI,CAAC4B,OAAO;QACvC;MACF,EAAE,OAAOnB,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK;QAC9BrE,SAAS,CAACqE,KAAK,CAAC,QAAQ;MAC1B,UAAU;QACRpD,OAAO,CAACmB,KAAI,GAAI,KAAI;MACtB;IACF;;IAEA;IACA,MAAMkF,gBAAe,GAAIA,CAACzC,IAAI,EAAE0C,cAAc,KAAK;MACjD/F,QAAQ,CAACY,KAAI,GAAImF,cAAa;MAC9BjD,OAAO,CAACpB,GAAG,CAAC,SAAS,EAAE1B,QAAQ,CAACY,KAAK;IACvC;;IAEA;IACA,MAAMoF,YAAW,GAAIA,CAAA,KAAM;MACzB,MAAMC,SAAQ,GAAIlB,QAAQ,CAACmB,aAAa,CAAC,4BAA4B;MACrE,OAAOD,SAAQ,GAAIA,SAAS,CAACrF,KAAI,GAAI,EAAC;IACxC;;IAEA;IACA,MAAMuF,YAAW,GAAI,MAAAA,CAAA,KAAY;MAC/BrD,OAAO,CAACpB,GAAG,CAAC,YAAY,EAAE1B,QAAQ,CAACY,KAAK;MAExC,IAAI,CAACZ,QAAQ,CAACY,KAAI,IAAKZ,QAAQ,CAACY,KAAK,CAACsD,MAAK,KAAM,CAAC,EAAE;QAClD1F,SAAS,CAAC2F,OAAO,CAAC,WAAW;QAC7B;MACF;MAEA,IAAI;QACFzE,SAAS,CAACkB,KAAI,GAAI,IAAG;;QAErB;QACA,KAAK,MAAMyC,IAAG,IAAKrD,QAAQ,CAACY,KAAK,EAAE;UACjC,MAAMwF,QAAO,GAAI,IAAIC,QAAQ,CAAC;UAC9BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEjD,IAAI,CAACkD,GAAG;UAEhC,MAAMpB,OAAM,GAAI;YACd,cAAc,EAAE;UAClB;;UAEA;UACA,MAAMc,SAAQ,GAAID,YAAY,CAAC;UAC/B,IAAIC,SAAS,EAAE;YACbd,OAAO,CAAC,aAAa,IAAIc,SAAQ;UACnC;UAEAnD,OAAO,CAACpB,GAAG,CAAC,OAAO,EAAE2B,IAAI,CAAClE,IAAI;UAC9B,MAAMD,KAAK,CAACmF,IAAI,CAAC,sBAAsB,EAAE+B,QAAQ,EAAE;YAAEjB;UAAQ,CAAC;QAChE;QAEA3G,SAAS,CAAC6D,OAAO,CAAC,UAAU;QAC5B1C,gBAAgB,CAACiB,KAAI,GAAI,KAAI;QAC7B4F,WAAW,CAAC;QACZzD,SAAS,CAAC;QACVvD,IAAI,CAAC,eAAe;MAEtB,EAAE,OAAOqD,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,OAAO,EAAEA,KAAK;QAC5B,IAAIA,KAAK,CAACd,QAAO,IAAKc,KAAK,CAACd,QAAQ,CAACK,IAAG,IAAKS,KAAK,CAACd,QAAQ,CAACK,IAAI,CAAC4B,OAAO,EAAE;UACxExF,SAAS,CAACqE,KAAK,CAACA,KAAK,CAACd,QAAQ,CAACK,IAAI,CAAC4B,OAAO;QAC7C,OAAO;UACLxF,SAAS,CAACqE,KAAK,CAAC,cAAc;QAChC;MACF,UAAU;QACRnD,SAAS,CAACkB,KAAI,GAAI,KAAI;MACxB;IACF;;IAEA;IACA,MAAM6F,mBAAkB,GAAIA,CAAC1E,QAAQ,EAAEsB,IAAI,KAAK;MAC9C7E,SAAS,CAAC6D,OAAO,CAAC,MAAMgB,IAAI,CAAClE,IAAI,OAAO;IAC1C;;IAEA;IACA,MAAMuH,iBAAgB,GAAIA,CAAC7D,KAAK,EAAEQ,IAAI,KAAK;MACzC7E,SAAS,CAACqE,KAAK,CAAC,MAAMQ,IAAI,CAAClE,IAAI,OAAO;IACxC;;IAEA;IACA,MAAMwH,YAAW,GAAIA,CAAA,KAAM;MACzB,IAAI7G,cAAc,CAACc,KAAK,EAAE;QACxBd,cAAc,CAACc,KAAK,CAAC8E,KAAK,CAAC;MAC7B;IACF;;IAEA;IACA,MAAMkB,kBAAiB,GAAKC,KAAK,IAAK;MACpC,MAAM3G,KAAI,GAAI4G,KAAK,CAACC,IAAI,CAACF,KAAK,CAACG,MAAM,CAAC9G,KAAK;MAC3CD,WAAW,CAACW,KAAI,GAAIV,KAAI;MACxB4C,OAAO,CAACpB,GAAG,CAAC,WAAW,EAAExB,KAAK;IAChC;;IAEA;IACA,MAAM+G,kBAAiB,GAAI,MAAAA,CAAA,KAAY;MACrC,IAAIhH,WAAW,CAACW,KAAK,CAACsD,MAAK,KAAM,CAAC,EAAE;QAClC1F,SAAS,CAAC2F,OAAO,CAAC,YAAY;QAC9B;MACF;MAEA,IAAI;QACFzE,SAAS,CAACkB,KAAI,GAAI,IAAG;QACrB,IAAIsG,YAAW,GAAI;QACnB,IAAIC,SAAQ,GAAI;;QAEhB;QACA,KAAK,MAAM9D,IAAG,IAAKpD,WAAW,CAACW,KAAK,EAAE;UACpC,IAAI;YACF,MAAMwF,QAAO,GAAI,IAAIC,QAAQ,CAAC;YAC9BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEjD,IAAI;YAE5B,MAAM8B,OAAM,GAAI;cACd,cAAc,EAAE;YAClB;;YAEA;YACA,MAAMc,SAAQ,GAAID,YAAY,CAAC;YAC/B,IAAIC,SAAS,EAAE;cACbd,OAAO,CAAC,aAAa,IAAIc,SAAQ;YACnC;YAEAnD,OAAO,CAACpB,GAAG,CAAC,OAAO,EAAE2B,IAAI,CAAClE,IAAI;YAC9B,MAAMD,KAAK,CAACmF,IAAI,CAAC,sBAAsB,EAAE+B,QAAQ,EAAE;cAAEjB;YAAQ,CAAC;YAC9D+B,YAAY,EAAC;UACf,EAAE,OAAOrE,KAAK,EAAE;YACdC,OAAO,CAACD,KAAK,CAAC,MAAMQ,IAAI,CAAClE,IAAI,QAAQ,EAAE0D,KAAK;YAC5CsE,SAAS,EAAC;UACZ;QACF;QAEA,IAAID,YAAW,GAAI,CAAC,EAAE;UACpB1I,SAAS,CAAC6D,OAAO,CAAC,QAAQ6E,YAAY,OAAOC,SAAQ,GAAI,IAAI,IAAIA,SAAS,QAAO,GAAI,EAAE,EAAE;UACzFvH,sBAAsB,CAACgB,KAAI,GAAI,KAAI;UACnCwG,iBAAiB,CAAC;UAClBrE,SAAS,CAAC;UACVvD,IAAI,CAAC,eAAe;QACtB,OAAO;UACLhB,SAAS,CAACqE,KAAK,CAAC,UAAU;QAC5B;MAEF,EAAE,OAAOA,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,UAAU,EAAEA,KAAK;QAC/BrE,SAAS,CAACqE,KAAK,CAAC,SAAS;MAC3B,UAAU;QACRnD,SAAS,CAACkB,KAAI,GAAI,KAAI;MACxB;IACF;;IAEA;IACA,MAAMwG,iBAAgB,GAAIA,CAAA,KAAM;MAC9BnH,WAAW,CAACW,KAAI,GAAI,EAAC;MACrB,IAAId,cAAc,CAACc,KAAK,EAAE;QACxBd,cAAc,CAACc,KAAK,CAACA,KAAI,GAAI,EAAC;MAChC;IACF;;IAEA;IACA,MAAM4F,WAAU,GAAIA,CAAA,KAAM;MACxBxG,QAAQ,CAACY,KAAI,GAAI,EAAC;MAClB,IAAIf,SAAS,CAACe,KAAK,EAAE;QACnBf,SAAS,CAACe,KAAK,CAACyG,UAAU,CAAC;MAC7B;IACF;IAEA/I,SAAS,CAAC,MAAM;MACdyE,SAAS,CAAC;MACVjB,aAAa,CAAC;IAChB,CAAC;IAED,OAAO;MACLrC,OAAO;MACPC,SAAS;MACTC,gBAAgB;MAChBC,sBAAsB;MACtBC,SAAS;MACTC,cAAc;MACdC,YAAY;MACZC,QAAQ;MACRC,WAAW;MACXC,KAAK;MACLC,aAAa;MACbC,WAAW;MACXC,QAAQ;MACRC,KAAK;MACLC,UAAU;MACVC,cAAc;MACdC,cAAc;MACdC,SAAS;MACTC,UAAU;MACVG,SAAS;MACTC,gBAAgB;MAChBI,cAAc;MACd4B,SAAS;MACTjB,aAAa;MACbmB,YAAY;MACZC,qBAAqB;MACrBE,cAAc;MACdK,YAAY;MACZQ,iBAAiB;MACjBO,mBAAmB;MACnBqB,YAAY;MACZC,gBAAgB;MAChBK,YAAY;MACZM,mBAAmB;MACnBC,iBAAiB;MACjBC,YAAY;MACZC,kBAAkB;MAClBK,kBAAkB;MAClBT,WAAW;MACXY;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { createElementVNode as _createElementVNode, createCommentVNode as _createCommentVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, createBlock as _createBlock, createTextVNode as _createTextVNode, toDisplayString as _toDisplayString, resolveDirective as _resolveDirective, withDirectives as _withDirectives } from \"vue\";\nconst _hoisted_1 = {\n  class: \"file-manager\"\n};\nconst _hoisted_2 = {\n  class: \"toolbar\"\n};\nconst _hoisted_3 = {\n  class: \"toolbar-left\"\n};\nconst _hoisted_4 = {\n  class: \"toolbar-right\"\n};\nconst _hoisted_5 = {\n  class: \"file-table\"\n};\nconst _hoisted_6 = {\n  class: \"file-name\"\n};\nconst _hoisted_7 = {\n  class: \"pagination\"\n};\nconst _hoisted_8 = {\n  class: \"dialog-footer\"\n};\nconst _hoisted_9 = {\n  class: \"folder-upload-area\"\n};\nconst _hoisted_10 = {\n  key: 0,\n  class: \"folder-files-preview\"\n};\nconst _hoisted_11 = {\n  class: \"files-list\"\n};\nconst _hoisted_12 = {\n  key: 0,\n  class: \"more-files\"\n};\nconst _hoisted_13 = {\n  class: \"dialog-footer\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_Search = _resolveComponent(\"Search\");\n  const _component_el_icon = _resolveComponent(\"el-icon\");\n  const _component_el_input = _resolveComponent(\"el-input\");\n  const _component_el_option = _resolveComponent(\"el-option\");\n  const _component_el_select = _resolveComponent(\"el-select\");\n  const _component_Upload = _resolveComponent(\"Upload\");\n  const _component_el_button = _resolveComponent(\"el-button\");\n  const _component_FolderOpened = _resolveComponent(\"FolderOpened\");\n  const _component_Delete = _resolveComponent(\"Delete\");\n  const _component_Download = _resolveComponent(\"Download\");\n  const _component_Refresh = _resolveComponent(\"Refresh\");\n  const _component_el_table_column = _resolveComponent(\"el-table-column\");\n  const _component_Warning = _resolveComponent(\"Warning\");\n  const _component_el_tag = _resolveComponent(\"el-tag\");\n  const _component_el_table = _resolveComponent(\"el-table\");\n  const _component_el_pagination = _resolveComponent(\"el-pagination\");\n  const _component_upload_filled = _resolveComponent(\"upload-filled\");\n  const _component_el_upload = _resolveComponent(\"el-upload\");\n  const _component_el_dialog = _resolveComponent(\"el-dialog\");\n  const _directive_loading = _resolveDirective(\"loading\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_cache[24] || (_cache[24] = _createElementVNode(\"div\", {\n    class: \"page-header\"\n  }, [_createElementVNode(\"h2\", null, \"文件管理\")], -1 /* CACHED */)), _createCommentVNode(\" 操作工具栏 \"), _createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_createVNode(_component_el_input, {\n    modelValue: $setup.searchText,\n    \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $setup.searchText = $event),\n    placeholder: \"搜索文件名\",\n    style: {\n      \"width\": \"200px\",\n      \"margin-right\": \"10px\"\n    },\n    clearable: \"\",\n    onInput: $setup.handleSearch\n  }, {\n    prefix: _withCtx(() => [_createVNode(_component_el_icon, null, {\n      default: _withCtx(() => [_createVNode(_component_Search)]),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onInput\"]), _createVNode(_component_el_select, {\n    modelValue: $setup.filterCategory,\n    \"onUpdate:modelValue\": _cache[1] || (_cache[1] = $event => $setup.filterCategory = $event),\n    placeholder: \"选择类别\",\n    style: {\n      \"width\": \"150px\",\n      \"margin-right\": \"10px\"\n    },\n    clearable: \"\",\n    onChange: $setup.loadFiles\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_option, {\n      label: \"全部类别\",\n      value: \"\"\n    }), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.categories, category => {\n      return _openBlock(), _createBlock(_component_el_option, {\n        key: category.value,\n        label: category.label,\n        value: category.value\n      }, null, 8 /* PROPS */, [\"label\", \"value\"]);\n    }), 128 /* KEYED_FRAGMENT */))]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onChange\"]), _createVNode(_component_el_select, {\n    modelValue: $setup.filterOrganism,\n    \"onUpdate:modelValue\": _cache[2] || (_cache[2] = $event => $setup.filterOrganism = $event),\n    placeholder: \"选择生物体\",\n    style: {\n      \"width\": \"150px\"\n    },\n    clearable: \"\",\n    onChange: $setup.loadFiles\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_option, {\n      label: \"全部生物体\",\n      value: \"\"\n    }), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.organisms, organism => {\n      return _openBlock(), _createBlock(_component_el_option, {\n        key: organism,\n        label: organism,\n        value: organism\n      }, null, 8 /* PROPS */, [\"label\", \"value\"]);\n    }), 128 /* KEYED_FRAGMENT */))]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onChange\"])]), _createElementVNode(\"div\", _hoisted_4, [_createVNode(_component_el_button, {\n    type: \"primary\",\n    onClick: _cache[3] || (_cache[3] = $event => $setup.showUploadDialog = true)\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n      default: _withCtx(() => [_createVNode(_component_Upload)]),\n      _: 1 /* STABLE */\n    }), _cache[13] || (_cache[13] = _createTextVNode(\" 上传文件 \"))]),\n    _: 1 /* STABLE */,\n    __: [13]\n  }), _createVNode(_component_el_button, {\n    type: \"success\",\n    onClick: _cache[4] || (_cache[4] = $event => $setup.showFolderUploadDialog = true)\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n      default: _withCtx(() => [_createVNode(_component_FolderOpened)]),\n      _: 1 /* STABLE */\n    }), _cache[14] || (_cache[14] = _createTextVNode(\" 上传文件夹 \"))]),\n    _: 1 /* STABLE */,\n    __: [14]\n  }), _createVNode(_component_el_button, {\n    type: \"danger\",\n    disabled: !$setup.selectedFiles || $setup.selectedFiles.length === 0,\n    onClick: $setup.handleBatchDelete\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n      default: _withCtx(() => [_createVNode(_component_Delete)]),\n      _: 1 /* STABLE */\n    }), _createTextVNode(\" 批量删除 (\" + _toDisplayString($setup.selectedFiles ? $setup.selectedFiles.length : 0) + \") \", 1 /* TEXT */)]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"disabled\", \"onClick\"]), _createVNode(_component_el_button, {\n    type: \"primary\",\n    disabled: !$setup.selectedFiles || $setup.selectedFiles.length === 0,\n    onClick: _ctx.handleBatchDownload\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n      default: _withCtx(() => [_createVNode(_component_Download)]),\n      _: 1 /* STABLE */\n    }), _createTextVNode(\" 批量下载 (\" + _toDisplayString($setup.selectedFiles ? $setup.selectedFiles.length : 0) + \") \", 1 /* TEXT */)]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"disabled\", \"onClick\"]), _createVNode(_component_el_button, {\n    type: \"success\",\n    onClick: $setup.handleRescan\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n      default: _withCtx(() => [_createVNode(_component_Refresh)]),\n      _: 1 /* STABLE */\n    }), _cache[15] || (_cache[15] = _createTextVNode(\" 重新扫描 \"))]),\n    _: 1 /* STABLE */,\n    __: [15]\n  }, 8 /* PROPS */, [\"onClick\"])])]), _createCommentVNode(\" 文件列表表格 \"), _createElementVNode(\"div\", _hoisted_5, [_withDirectives((_openBlock(), _createBlock(_component_el_table, {\n    data: $setup.files,\n    onSelectionChange: $setup.handleSelectionChange,\n    stripe: \"\",\n    style: {\n      \"width\": \"100%\"\n    }\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_table_column, {\n      type: \"selection\",\n      width: \"55\"\n    }), _createVNode(_component_el_table_column, {\n      prop: \"name\",\n      label: \"文件名\",\n      \"min-width\": \"200\"\n    }, {\n      default: _withCtx(scope => [_createElementVNode(\"div\", _hoisted_6, [!scope.row.exists ? (_openBlock(), _createBlock(_component_el_icon, {\n        key: 0,\n        class: \"missing-icon\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_Warning)]),\n        _: 1 /* STABLE */\n      })) : _createCommentVNode(\"v-if\", true), _createTextVNode(\" \" + _toDisplayString(scope.row.name), 1 /* TEXT */)])]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_table_column, {\n      prop: \"organism\",\n      label: \"生物体\",\n      width: \"120\"\n    }), _createVNode(_component_el_table_column, {\n      prop: \"category\",\n      label: \"类别\",\n      width: \"150\"\n    }, {\n      default: _withCtx(scope => [_createVNode(_component_el_tag, {\n        size: \"small\"\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.getCategoryLabel(scope.row.category)), 1 /* TEXT */)]),\n        _: 2 /* DYNAMIC */\n      }, 1024 /* DYNAMIC_SLOTS */)]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_table_column, {\n      prop: \"file_type\",\n      label: \"文件类型\",\n      width: \"100\"\n    }), _createVNode(_component_el_table_column, {\n      prop: \"size\",\n      label: \"文件大小\",\n      width: \"120\"\n    }, {\n      default: _withCtx(scope => [_createTextVNode(_toDisplayString($setup.formatFileSize(scope.row.size)), 1 /* TEXT */)]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_table_column, {\n      prop: \"created_at\",\n      label: \"创建时间\",\n      width: \"160\"\n    }), _createVNode(_component_el_table_column, {\n      label: \"状态\",\n      width: \"80\"\n    }, {\n      default: _withCtx(scope => [_createVNode(_component_el_tag, {\n        type: scope.row.exists ? 'success' : 'danger',\n        size: \"small\"\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString(scope.row.exists ? '正常' : '缺失'), 1 /* TEXT */)]),\n        _: 2 /* DYNAMIC */\n      }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"type\"])]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_table_column, {\n      label: \"操作\",\n      width: \"150\",\n      fixed: \"right\"\n    }, {\n      default: _withCtx(scope => [_createVNode(_component_el_button, {\n        type: \"primary\",\n        size: \"small\",\n        disabled: !scope.row.exists,\n        onClick: $event => $setup.handleDownload(scope.row)\n      }, {\n        default: _withCtx(() => _cache[16] || (_cache[16] = [_createTextVNode(\" 下载 \")])),\n        _: 2 /* DYNAMIC */,\n        __: [16]\n      }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"disabled\", \"onClick\"]), _createVNode(_component_el_button, {\n        type: \"danger\",\n        size: \"small\",\n        onClick: $event => $setup.handleDelete(scope.row)\n      }, {\n        default: _withCtx(() => _cache[17] || (_cache[17] = [_createTextVNode(\" 删除 \")])),\n        _: 2 /* DYNAMIC */,\n        __: [17]\n      }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"])]),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"data\", \"onSelectionChange\"])), [[_directive_loading, $setup.loading]]), _createCommentVNode(\" 分页 \"), _createElementVNode(\"div\", _hoisted_7, [_createVNode(_component_el_pagination, {\n    \"current-page\": $setup.currentPage,\n    \"onUpdate:currentPage\": _cache[5] || (_cache[5] = $event => $setup.currentPage = $event),\n    \"page-size\": $setup.pageSize,\n    \"onUpdate:pageSize\": _cache[6] || (_cache[6] = $event => $setup.pageSize = $event),\n    \"page-sizes\": [10, 20, 50, 100],\n    total: $setup.total,\n    layout: \"total, sizes, prev, pager, next, jumper\",\n    onSizeChange: $setup.loadFiles,\n    onCurrentChange: $setup.loadFiles\n  }, null, 8 /* PROPS */, [\"current-page\", \"page-size\", \"total\", \"onSizeChange\", \"onCurrentChange\"])])]), _createCommentVNode(\" 上传文件对话框 \"), _createVNode(_component_el_dialog, {\n    modelValue: $setup.showUploadDialog,\n    \"onUpdate:modelValue\": _cache[8] || (_cache[8] = $event => $setup.showUploadDialog = $event),\n    title: \"上传文件\",\n    width: \"500px\",\n    onClose: $setup.resetUpload\n  }, {\n    footer: _withCtx(() => [_createElementVNode(\"span\", _hoisted_8, [_createVNode(_component_el_button, {\n      onClick: _cache[7] || (_cache[7] = $event => $setup.showUploadDialog = false)\n    }, {\n      default: _withCtx(() => _cache[20] || (_cache[20] = [_createTextVNode(\"取消\")])),\n      _: 1 /* STABLE */,\n      __: [20]\n    }), _createVNode(_component_el_button, {\n      type: \"primary\",\n      loading: $setup.uploading,\n      onClick: $setup.handleUpload\n    }, {\n      default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.uploading ? '上传中...' : '开始上传'), 1 /* TEXT */)]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"loading\", \"onClick\"])])]),\n    default: _withCtx(() => [_createVNode(_component_el_upload, {\n      ref: \"uploadRef\",\n      action: $setup.uploadUrl,\n      \"auto-upload\": false,\n      \"on-change\": $setup.handleFileChange,\n      \"on-success\": $setup.handleUploadSuccess,\n      \"on-error\": $setup.handleUploadError,\n      \"file-list\": $setup.fileList,\n      drag: \"\",\n      multiple: \"\"\n    }, {\n      tip: _withCtx(() => _cache[18] || (_cache[18] = [_createElementVNode(\"div\", {\n        class: \"el-upload__tip\"\n      }, \" 支持多文件上传，系统会自动识别文件类型和分类 \", -1 /* CACHED */)])),\n      default: _withCtx(() => [_createVNode(_component_el_icon, {\n        class: \"el-icon--upload\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_upload_filled)]),\n        _: 1 /* STABLE */\n      }), _cache[19] || (_cache[19] = _createElementVNode(\"div\", {\n        class: \"el-upload__text\"\n      }, [_createTextVNode(\" 将文件拖到此处，或\"), _createElementVNode(\"em\", null, \"点击上传\")], -1 /* CACHED */))]),\n      _: 1 /* STABLE */,\n      __: [19]\n    }, 8 /* PROPS */, [\"action\", \"on-change\", \"on-success\", \"on-error\", \"file-list\"])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onClose\"]), _createCommentVNode(\" 上传文件夹对话框 \"), _createVNode(_component_el_dialog, {\n    modelValue: $setup.showFolderUploadDialog,\n    \"onUpdate:modelValue\": _cache[12] || (_cache[12] = $event => $setup.showFolderUploadDialog = $event),\n    title: \"上传文件夹\",\n    width: \"500px\",\n    onClose: $setup.resetFolderUpload\n  }, {\n    footer: _withCtx(() => [_createElementVNode(\"span\", _hoisted_13, [_createVNode(_component_el_button, {\n      onClick: _cache[11] || (_cache[11] = $event => $setup.showFolderUploadDialog = false)\n    }, {\n      default: _withCtx(() => _cache[23] || (_cache[23] = [_createTextVNode(\"取消\")])),\n      _: 1 /* STABLE */,\n      __: [23]\n    }), _createVNode(_component_el_button, {\n      type: \"primary\",\n      loading: $setup.uploading,\n      disabled: $setup.folderFiles.length === 0,\n      onClick: $setup.handleFolderUpload\n    }, {\n      default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.uploading ? '上传中...' : `开始上传 (${$setup.folderFiles.length} 个文件)`), 1 /* TEXT */)]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"loading\", \"disabled\", \"onClick\"])])]),\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_9, [_createElementVNode(\"input\", {\n      ref: \"folderInputRef\",\n      type: \"file\",\n      webkitdirectory: \"\",\n      multiple: \"\",\n      style: {\n        \"display\": \"none\"\n      },\n      onChange: _cache[9] || (_cache[9] = (...args) => $setup.handleFolderSelect && $setup.handleFolderSelect(...args))\n    }, null, 544 /* NEED_HYDRATION, NEED_PATCH */), _createElementVNode(\"div\", {\n      class: \"folder-drop-zone\",\n      onClick: _cache[10] || (_cache[10] = (...args) => $setup.selectFolder && $setup.selectFolder(...args))\n    }, [_createVNode(_component_el_icon, {\n      class: \"folder-icon\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_FolderOpened)]),\n      _: 1 /* STABLE */\n    }), _cache[21] || (_cache[21] = _createElementVNode(\"div\", {\n      class: \"folder-upload-text\"\n    }, \" 点击选择文件夹 \", -1 /* CACHED */)), _cache[22] || (_cache[22] = _createElementVNode(\"div\", {\n      class: \"folder-upload-tip\"\n    }, \" 将上传文件夹中的所有文件 \", -1 /* CACHED */))]), $setup.folderFiles.length > 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_10, [_createElementVNode(\"h4\", null, \"将要上传的文件 (\" + _toDisplayString($setup.folderFiles.length) + \" 个):\", 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_11, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.folderFiles.slice(0, 10), file => {\n      return _openBlock(), _createElementBlock(\"div\", {\n        key: file.name,\n        class: \"file-item\"\n      }, _toDisplayString(file.webkitRelativePath || file.name), 1 /* TEXT */);\n    }), 128 /* KEYED_FRAGMENT */)), $setup.folderFiles.length > 10 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_12, \" ... 还有 \" + _toDisplayString($setup.folderFiles.length - 10) + \" 个文件 \", 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true)])])) : _createCommentVNode(\"v-if\", true)])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onClose\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_createCommentVNode", "_hoisted_2", "_hoisted_3", "_createVNode", "_component_el_input", "$setup", "searchText", "$event", "placeholder", "style", "clearable", "onInput", "handleSearch", "prefix", "_withCtx", "_component_el_icon", "_component_Search", "_component_el_select", "filterCategory", "onChange", "loadFiles", "_component_el_option", "label", "value", "_Fragment", "_renderList", "categories", "category", "_createBlock", "key", "filterOrganism", "organisms", "organism", "_hoisted_4", "_component_el_button", "type", "onClick", "_cache", "showUploadDialog", "_component_Upload", "showFolderUploadDialog", "_component_FolderOpened", "disabled", "selectedFiles", "length", "handleBatchDelete", "_component_Delete", "_toDisplayString", "_ctx", "handleBatchDownload", "_component_Download", "handleRescan", "_component_Refresh", "_hoisted_5", "_component_el_table", "data", "files", "onSelectionChange", "handleSelectionChange", "stripe", "_component_el_table_column", "width", "prop", "default", "scope", "_hoisted_6", "row", "exists", "_component_Warning", "name", "_component_el_tag", "size", "getCategoryLabel", "formatFileSize", "fixed", "handleDownload", "handleDelete", "loading", "_hoisted_7", "_component_el_pagination", "currentPage", "pageSize", "total", "layout", "onSizeChange", "onCurrentChange", "_component_el_dialog", "title", "onClose", "resetUpload", "footer", "_hoisted_8", "uploading", "handleUpload", "_component_el_upload", "ref", "action", "uploadUrl", "handleFileChange", "handleUploadSuccess", "handleUploadError", "fileList", "drag", "multiple", "tip", "_component_upload_filled", "resetFolderUpload", "_hoisted_13", "folderFiles", "handleFolderUpload", "_hoisted_9", "webkitdirectory", "args", "handleFolderSelect", "selectFolder", "_hoisted_10", "_hoisted_11", "slice", "file", "webkitRelativePath", "_hoisted_12"], "sources": ["D:\\gene_manage_system\\vue_project\\src\\views\\AdminFileManager.vue"], "sourcesContent": ["<template>\n  <div class=\"file-manager\">\n    <div class=\"page-header\">\n      <h2>文件管理</h2>\n    </div>\n    \n    <!-- 操作工具栏 -->\n    <div class=\"toolbar\">\n      <div class=\"toolbar-left\">\n        <el-input\n          v-model=\"searchText\"\n          placeholder=\"搜索文件名\"\n          style=\"width: 200px; margin-right: 10px;\"\n          clearable\n          @input=\"handleSearch\"\n        >\n          <template #prefix>\n            <el-icon><Search /></el-icon>\n          </template>\n        </el-input>\n\n        <el-select\n          v-model=\"filterCategory\"\n          placeholder=\"选择类别\"\n          style=\"width: 150px; margin-right: 10px;\"\n          clearable\n          @change=\"loadFiles\"\n        >\n          <el-option label=\"全部类别\" value=\"\" />\n          <el-option\n            v-for=\"category in categories\"\n            :key=\"category.value\"\n            :label=\"category.label\"\n            :value=\"category.value\"\n          />\n        </el-select>\n\n        <el-select\n          v-model=\"filterOrganism\"\n          placeholder=\"选择生物体\"\n          style=\"width: 150px;\"\n          clearable\n          @change=\"loadFiles\"\n        >\n          <el-option label=\"全部生物体\" value=\"\" />\n          <el-option\n            v-for=\"organism in organisms\"\n            :key=\"organism\"\n            :label=\"organism\"\n            :value=\"organism\"\n          />\n        </el-select>\n      </div>\n\n      <div class=\"toolbar-right\">\n        <el-button type=\"primary\" @click=\"showUploadDialog = true\">\n          <el-icon><Upload /></el-icon>\n          上传文件\n        </el-button>\n        <el-button type=\"success\" @click=\"showFolderUploadDialog = true\">\n          <el-icon><FolderOpened /></el-icon>\n          上传文件夹\n        </el-button>\n        <el-button\n          type=\"danger\"\n          :disabled=\"!selectedFiles || selectedFiles.length === 0\"\n          @click=\"handleBatchDelete\"\n        >\n          <el-icon><Delete /></el-icon>\n          批量删除 ({{ selectedFiles ? selectedFiles.length : 0 }})\n        </el-button>\n        <el-button\n          type=\"primary\"\n          :disabled=\"!selectedFiles || selectedFiles.length === 0\"\n          @click=\"handleBatchDownload\"\n        >\n          <el-icon><Download /></el-icon>\n          批量下载 ({{ selectedFiles ? selectedFiles.length : 0 }})\n        </el-button>\n        <el-button type=\"success\" @click=\"handleRescan\">\n          <el-icon><Refresh /></el-icon>\n          重新扫描\n        </el-button>\n      </div>\n    </div>\n    \n    <!-- 文件列表表格 -->\n    <div class=\"file-table\">\n      <el-table\n        :data=\"files\"\n        v-loading=\"loading\"\n        @selection-change=\"handleSelectionChange\"\n        stripe\n        style=\"width: 100%\"\n      >\n        <el-table-column type=\"selection\" width=\"55\" />\n        \n        <el-table-column prop=\"name\" label=\"文件名\" min-width=\"200\">\n          <template #default=\"scope\">\n            <div class=\"file-name\">\n              <el-icon v-if=\"!scope.row.exists\" class=\"missing-icon\"><Warning /></el-icon>\n              {{ scope.row.name }}\n            </div>\n          </template>\n        </el-table-column>\n        \n        <el-table-column prop=\"organism\" label=\"生物体\" width=\"120\" />\n        \n        <el-table-column prop=\"category\" label=\"类别\" width=\"150\">\n          <template #default=\"scope\">\n            <el-tag size=\"small\">{{ getCategoryLabel(scope.row.category) }}</el-tag>\n          </template>\n        </el-table-column>\n        \n        <el-table-column prop=\"file_type\" label=\"文件类型\" width=\"100\" />\n        \n        <el-table-column prop=\"size\" label=\"文件大小\" width=\"120\">\n          <template #default=\"scope\">\n            {{ formatFileSize(scope.row.size) }}\n          </template>\n        </el-table-column>\n        \n        <el-table-column prop=\"created_at\" label=\"创建时间\" width=\"160\" />\n        \n        <el-table-column label=\"状态\" width=\"80\">\n          <template #default=\"scope\">\n            <el-tag :type=\"scope.row.exists ? 'success' : 'danger'\" size=\"small\">\n              {{ scope.row.exists ? '正常' : '缺失' }}\n            </el-tag>\n          </template>\n        </el-table-column>\n        \n        <el-table-column label=\"操作\" width=\"150\" fixed=\"right\">\n          <template #default=\"scope\">\n            <el-button \n              type=\"primary\" \n              size=\"small\" \n              :disabled=\"!scope.row.exists\"\n              @click=\"handleDownload(scope.row)\"\n            >\n              下载\n            </el-button>\n            <el-button \n              type=\"danger\" \n              size=\"small\"\n              @click=\"handleDelete(scope.row)\"\n            >\n              删除\n            </el-button>\n          </template>\n        </el-table-column>\n      </el-table>\n      \n      <!-- 分页 -->\n      <div class=\"pagination\">\n        <el-pagination\n          v-model:current-page=\"currentPage\"\n          v-model:page-size=\"pageSize\"\n          :page-sizes=\"[10, 20, 50, 100]\"\n          :total=\"total\"\n          layout=\"total, sizes, prev, pager, next, jumper\"\n          @size-change=\"loadFiles\"\n          @current-change=\"loadFiles\"\n        />\n      </div>\n    </div>\n    \n    <!-- 上传文件对话框 -->\n    <el-dialog\n      v-model=\"showUploadDialog\"\n      title=\"上传文件\"\n      width=\"500px\"\n      @close=\"resetUpload\"\n    >\n      <el-upload\n        ref=\"uploadRef\"\n        :action=\"uploadUrl\"\n        :auto-upload=\"false\"\n        :on-change=\"handleFileChange\"\n        :on-success=\"handleUploadSuccess\"\n        :on-error=\"handleUploadError\"\n        :file-list=\"fileList\"\n        drag\n        multiple\n      >\n        <el-icon class=\"el-icon--upload\"><upload-filled /></el-icon>\n        <div class=\"el-upload__text\">\n          将文件拖到此处，或<em>点击上传</em>\n        </div>\n        <template #tip>\n          <div class=\"el-upload__tip\">\n            支持多文件上传，系统会自动识别文件类型和分类\n          </div>\n        </template>\n      </el-upload>\n\n      <template #footer>\n        <span class=\"dialog-footer\">\n          <el-button @click=\"showUploadDialog = false\">取消</el-button>\n          <el-button\n            type=\"primary\"\n            :loading=\"uploading\"\n            @click=\"handleUpload\"\n          >\n            {{ uploading ? '上传中...' : '开始上传' }}\n          </el-button>\n        </span>\n      </template>\n    </el-dialog>\n\n    <!-- 上传文件夹对话框 -->\n    <el-dialog\n      v-model=\"showFolderUploadDialog\"\n      title=\"上传文件夹\"\n      width=\"500px\"\n      @close=\"resetFolderUpload\"\n    >\n      <div class=\"folder-upload-area\">\n        <input\n          ref=\"folderInputRef\"\n          type=\"file\"\n          webkitdirectory\n          multiple\n          style=\"display: none\"\n          @change=\"handleFolderSelect\"\n        />\n        <div\n          class=\"folder-drop-zone\"\n          @click=\"selectFolder\"\n        >\n          <el-icon class=\"folder-icon\"><FolderOpened /></el-icon>\n          <div class=\"folder-upload-text\">\n            点击选择文件夹\n          </div>\n          <div class=\"folder-upload-tip\">\n            将上传文件夹中的所有文件\n          </div>\n        </div>\n\n        <div v-if=\"folderFiles.length > 0\" class=\"folder-files-preview\">\n          <h4>将要上传的文件 ({{ folderFiles.length }} 个):</h4>\n          <div class=\"files-list\">\n            <div\n              v-for=\"file in folderFiles.slice(0, 10)\"\n              :key=\"file.name\"\n              class=\"file-item\"\n            >\n              {{ file.webkitRelativePath || file.name }}\n            </div>\n            <div v-if=\"folderFiles.length > 10\" class=\"more-files\">\n              ... 还有 {{ folderFiles.length - 10 }} 个文件\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <template #footer>\n        <span class=\"dialog-footer\">\n          <el-button @click=\"showFolderUploadDialog = false\">取消</el-button>\n          <el-button\n            type=\"primary\"\n            :loading=\"uploading\"\n            :disabled=\"folderFiles.length === 0\"\n            @click=\"handleFolderUpload\"\n          >\n            {{ uploading ? '上传中...' : `开始上传 (${folderFiles.length} 个文件)` }}\n          </el-button>\n        </span>\n      </template>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { ref, reactive, onMounted, computed } from 'vue'\nimport { ElMessage, ElMessageBox } from 'element-plus'\nimport {\n  Upload, Delete, Download, Refresh, Search, Warning, UploadFilled, FolderOpened\n} from '@element-plus/icons-vue'\nimport axios from 'axios'\n\nexport default {\n  name: 'AdminFileManager',\n  components: {\n    Upload,\n    Delete,\n    Download,\n    Refresh,\n    Search,\n    Warning,\n    UploadFilled,\n    FolderOpened\n  },\n  emits: ['refresh-stats'],\n  setup(props, { emit }) {\n    const loading = ref(false)\n    const uploading = ref(false)\n    const showUploadDialog = ref(false)\n    const showFolderUploadDialog = ref(false)\n    const uploadRef = ref(null)\n    const folderInputRef = ref(null)\n    const fileList = ref([])\n    const folderFiles = ref([])\n\n    const files = ref([])\n    const selectedFiles = ref([])\n    const currentPage = ref(1)\n    const pageSize = ref(20)\n    const total = ref(0)\n    \n    const searchText = ref('')\n    const filterCategory = ref('')\n    const filterOrganism = ref('')\n    \n    const organisms = ref([])\n    \n    // 文件类别选项\n    const categories = [\n      { value: 'genome', label: '基因组序列' },\n      { value: 'transcriptome.all', label: '转录组-All' },\n      { value: 'transcriptome.root', label: '转录组-Root' },\n      { value: 'transcriptome.stem', label: '转录组-Stem' },\n      { value: 'transcriptome.leaf', label: '转录组-Leaf' },\n      { value: 'transcriptome.panicles', label: '转录组-Panicles' },\n      { value: 'transcriptome.shoot', label: '转录组-Shoot' },\n      { value: 'miRNA', label: '微RNA' },\n      { value: 'tRNA', label: '转运RNA' },\n      { value: 'rRNA', label: '核糖体RNA' },\n      { value: 'codon', label: '密码子' },\n      { value: 'centromere', label: '着丝粒' },\n      { value: 'TEs', label: '转座子' },\n      { value: 'annotation', label: '基因注释' },\n      { value: 'coreBlocks', label: '核心区块' },\n      { value: 'other', label: '其他' }\n    ]\n    \n    const uploadUrl = computed(() => {\n      return '/admin/files/upload/'\n    })\n    \n    // 获取类别标签\n    const getCategoryLabel = (value) => {\n      const category = categories.find(c => c.value === value)\n      return category ? category.label : value\n    }\n    \n    // 格式化文件大小\n    const formatFileSize = (bytes) => {\n      if (!bytes) return '0 B'\n      const k = 1024\n      const sizes = ['B', 'KB', 'MB', 'GB']\n      const i = Math.floor(Math.log(bytes) / Math.log(k))\n      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]\n    }\n    \n    // 加载所有生物体列表\n    const loadOrganisms = async () => {\n      try {\n        // 获取所有文件的生物体列表（不带过滤条件）\n        const response = await axios.get('/admin/files/', {\n          params: { page: 1, page_size: 1000 } // 获取足够多的数据来提取生物体\n        })\n\n        if (response.data.success) {\n          const uniqueOrganisms = [...new Set(response.data.data.map(f => f.organism))]\n          organisms.value = uniqueOrganisms.filter(o => o && o !== 'unknown')\n        }\n      } catch (error) {\n        console.error('加载生物体列表失败:', error)\n      }\n    }\n\n    // 加载文件列表\n    const loadFiles = async () => {\n      try {\n        loading.value = true\n        const params = {\n          page: currentPage.value,\n          page_size: pageSize.value,\n          search: searchText.value,\n          category: filterCategory.value,\n          organism: filterOrganism.value\n        }\n\n        const response = await axios.get('/admin/files/', { params })\n\n        if (response.data.success) {\n          files.value = response.data.data\n          total.value = response.data.total\n        }\n      } catch (error) {\n        console.error('加载文件列表失败:', error)\n        ElMessage.error('加载文件列表失败')\n      } finally {\n        loading.value = false\n      }\n    }\n\n    // 搜索处理\n    const handleSearch = () => {\n      currentPage.value = 1\n      loadFiles()\n    }\n\n    // 选择变化处理\n    const handleSelectionChange = (selection) => {\n      selectedFiles.value = selection || []\n    }\n\n    // 下载文件\n    const handleDownload = (file) => {\n      const downloadUrl = `/gd/api/manual_download/${file.name}`\n      window.open(downloadUrl, '_blank')\n    }\n\n    // 删除单个文件\n    const handleDelete = async (file) => {\n      try {\n        await ElMessageBox.confirm(\n          `确定要删除文件 \"${file.name}\" 吗？此操作不可恢复。`,\n          '确认删除',\n          {\n            confirmButtonText: '确定',\n            cancelButtonText: '取消',\n            type: 'warning'\n          }\n        )\n\n        const response = await axios.delete(`/admin/files/${file.id}/delete/`)\n\n        if (response.data.success) {\n          ElMessage.success(response.data.message)\n          loadFiles()\n          emit('refresh-stats')\n        } else {\n          ElMessage.error(response.data.message)\n        }\n      } catch (error) {\n        if (error !== 'cancel') {\n          console.error('删除文件失败:', error)\n          ElMessage.error('删除文件失败')\n        }\n      }\n    }\n\n    // 批量删除\n    const handleBatchDelete = async () => {\n      if (!selectedFiles.value || selectedFiles.value.length === 0) {\n        ElMessage.warning('请选择要删除的文件')\n        return\n      }\n\n      try {\n        await ElMessageBox.confirm(\n          `确定要删除选中的 ${selectedFiles.value.length} 个文件吗？此操作不可恢复。`,\n          '确认批量删除',\n          {\n            confirmButtonText: '确定',\n            cancelButtonText: '取消',\n            type: 'warning'\n          }\n        )\n\n        const fileIds = selectedFiles.value.map(f => f.id)\n        const response = await axios.post('/admin/files/batch-delete/', {\n          file_ids: fileIds\n        })\n\n        if (response.data.success) {\n          ElMessage.success(response.data.message)\n          selectedFiles.value = []\n          loadFiles()\n          emit('refresh-stats')\n        } else {\n          ElMessage.error(response.data.message)\n        }\n      } catch (error) {\n        if (error !== 'cancel') {\n          console.error('批量删除失败:', error)\n          ElMessage.error('批量删除失败')\n        }\n      }\n    }\n\n    // 重新扫描\n    const handleRescan = async () => {\n      try {\n        loading.value = true\n        const response = await axios.post('/admin/rescan/')\n\n        if (response.data.success) {\n          ElMessage.success(response.data.message)\n          loadFiles()\n          emit('refresh-stats')\n        } else {\n          ElMessage.error(response.data.message)\n        }\n      } catch (error) {\n        console.error('重新扫描失败:', error)\n        ElMessage.error('重新扫描失败')\n      } finally {\n        loading.value = false\n      }\n    }\n\n    // 文件变化处理\n    const handleFileChange = (file, uploadFileList) => {\n      fileList.value = uploadFileList\n      console.log('文件列表更新:', fileList.value)\n    }\n\n    // 获取CSRF token\n    const getCsrfToken = () => {\n      const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]')\n      return csrfToken ? csrfToken.value : ''\n    }\n\n    // 上传处理\n    const handleUpload = async () => {\n      console.log('开始上传，文件列表:', fileList.value)\n\n      if (!fileList.value || fileList.value.length === 0) {\n        ElMessage.warning('请选择要上传的文件')\n        return\n      }\n\n      try {\n        uploading.value = true\n\n        // 逐个上传文件\n        for (const file of fileList.value) {\n          const formData = new FormData()\n          formData.append('file', file.raw)\n\n          const headers = {\n            'Content-Type': 'multipart/form-data'\n          }\n\n          // 添加CSRF token（如果存在）\n          const csrfToken = getCsrfToken()\n          if (csrfToken) {\n            headers['X-CSRFToken'] = csrfToken\n          }\n\n          console.log('上传文件:', file.name)\n          await axios.post('/admin/files/upload/', formData, { headers })\n        }\n\n        ElMessage.success('所有文件上传成功')\n        showUploadDialog.value = false\n        resetUpload()\n        loadFiles()\n        emit('refresh-stats')\n\n      } catch (error) {\n        console.error('上传失败:', error)\n        if (error.response && error.response.data && error.response.data.message) {\n          ElMessage.error(error.response.data.message)\n        } else {\n          ElMessage.error('上传失败，请检查网络连接')\n        }\n      } finally {\n        uploading.value = false\n      }\n    }\n\n    // 上传成功处理\n    const handleUploadSuccess = (response, file) => {\n      ElMessage.success(`文件 ${file.name} 上传成功`)\n    }\n\n    // 上传失败处理\n    const handleUploadError = (error, file) => {\n      ElMessage.error(`文件 ${file.name} 上传失败`)\n    }\n\n    // 选择文件夹\n    const selectFolder = () => {\n      if (folderInputRef.value) {\n        folderInputRef.value.click()\n      }\n    }\n\n    // 处理文件夹选择\n    const handleFolderSelect = (event) => {\n      const files = Array.from(event.target.files)\n      folderFiles.value = files\n      console.log('选择的文件夹文件:', files)\n    }\n\n    // 文件夹上传处理\n    const handleFolderUpload = async () => {\n      if (folderFiles.value.length === 0) {\n        ElMessage.warning('请选择要上传的文件夹')\n        return\n      }\n\n      try {\n        uploading.value = true\n        let successCount = 0\n        let failCount = 0\n\n        // 逐个上传文件\n        for (const file of folderFiles.value) {\n          try {\n            const formData = new FormData()\n            formData.append('file', file)\n\n            const headers = {\n              'Content-Type': 'multipart/form-data'\n            }\n\n            // 添加CSRF token（如果存在）\n            const csrfToken = getCsrfToken()\n            if (csrfToken) {\n              headers['X-CSRFToken'] = csrfToken\n            }\n\n            console.log('上传文件:', file.name)\n            await axios.post('/admin/files/upload/', formData, { headers })\n            successCount++\n          } catch (error) {\n            console.error(`文件 ${file.name} 上传失败:`, error)\n            failCount++\n          }\n        }\n\n        if (successCount > 0) {\n          ElMessage.success(`成功上传 ${successCount} 个文件${failCount > 0 ? `，${failCount} 个文件失败` : ''}`)\n          showFolderUploadDialog.value = false\n          resetFolderUpload()\n          loadFiles()\n          emit('refresh-stats')\n        } else {\n          ElMessage.error('所有文件上传失败')\n        }\n\n      } catch (error) {\n        console.error('文件夹上传失败:', error)\n        ElMessage.error('文件夹上传失败')\n      } finally {\n        uploading.value = false\n      }\n    }\n\n    // 重置文件夹上传\n    const resetFolderUpload = () => {\n      folderFiles.value = []\n      if (folderInputRef.value) {\n        folderInputRef.value.value = ''\n      }\n    }\n\n    // 重置上传\n    const resetUpload = () => {\n      fileList.value = []\n      if (uploadRef.value) {\n        uploadRef.value.clearFiles()\n      }\n    }\n\n    onMounted(() => {\n      loadFiles()\n      loadOrganisms()\n    })\n\n    return {\n      loading,\n      uploading,\n      showUploadDialog,\n      showFolderUploadDialog,\n      uploadRef,\n      folderInputRef,\n      fileList,\n      folderFiles,\n      files,\n      selectedFiles,\n      currentPage,\n      pageSize,\n      total,\n      searchText,\n      filterCategory,\n      filterOrganism,\n      organisms,\n      categories,\n      uploadUrl,\n      getCategoryLabel,\n      formatFileSize,\n      loadFiles,\n      loadOrganisms,\n      handleSearch,\n      handleSelectionChange,\n      handleDownload,\n      handleDelete,\n      handleBatchDelete,\n      handleRescan,\n      handleFileChange,\n      handleUpload,\n      handleUploadSuccess,\n      handleUploadError,\n      selectFolder,\n      handleFolderSelect,\n      handleFolderUpload,\n      resetUpload,\n      resetFolderUpload\n    }\n  }\n}\n</script>\n\n<style scoped>\n.file-manager h2 {\n  margin-top: 0;\n  margin-bottom: 20px;\n  color: #333;\n}\n\n.toolbar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n  padding: 15px;\n  background: white;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n}\n\n.toolbar-left {\n  display: flex;\n  gap: 10px;\n}\n\n.toolbar-right {\n  display: flex;\n  align-items: center;\n}\n\n.file-table {\n  background: white;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n  overflow: hidden;\n}\n\n.file-name {\n  display: flex;\n  align-items: center;\n  gap: 5px;\n}\n\n.missing-icon {\n  color: #f56c6c;\n}\n\n.pagination {\n  padding: 20px;\n  text-align: right;\n}\n\n.dialog-footer {\n  display: flex;\n  justify-content: flex-end;\n  gap: 10px;\n}\n\n/* 文件夹上传样式 */\n.folder-upload-area {\n  padding: 20px 0;\n}\n\n.folder-drop-zone {\n  border: 2px dashed #d9d9d9;\n  border-radius: 6px;\n  padding: 40px;\n  text-align: center;\n  cursor: pointer;\n  transition: border-color 0.3s;\n}\n\n.folder-drop-zone:hover {\n  border-color: #409eff;\n}\n\n.folder-icon {\n  font-size: 48px;\n  color: #c0c4cc;\n  margin-bottom: 16px;\n}\n\n.folder-upload-text {\n  font-size: 16px;\n  color: #606266;\n  margin-bottom: 8px;\n}\n\n.folder-upload-tip {\n  font-size: 14px;\n  color: #909399;\n}\n\n.folder-files-preview {\n  margin-top: 20px;\n  padding: 16px;\n  background: #f5f7fa;\n  border-radius: 4px;\n}\n\n.folder-files-preview h4 {\n  margin: 0 0 12px 0;\n  color: #303133;\n  font-size: 14px;\n}\n\n.files-list {\n  max-height: 200px;\n  overflow-y: auto;\n}\n\n.file-item {\n  padding: 4px 0;\n  font-size: 13px;\n  color: #606266;\n  border-bottom: 1px solid #ebeef5;\n}\n\n.file-item:last-child {\n  border-bottom: none;\n}\n\n.more-files {\n  padding: 8px 0;\n  font-size: 13px;\n  color: #909399;\n  font-style: italic;\n}\n\n/* 页面标题样式 */\n.page-header {\n  margin-bottom: 20px;\n}\n\n.page-header h2 {\n  margin: 0 0 10px 0;\n  color: #1a56db;\n  font-size: 24px;\n  font-weight: 600;\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAc;;EAMlBA,KAAK,EAAC;AAAS;;EACbA,KAAK,EAAC;AAAc;;EA8CpBA,KAAK,EAAC;AAAe;;EAiCvBA,KAAK,EAAC;AAAY;;EAYVA,KAAK,EAAC;AAAW;;EAuDvBA,KAAK,EAAC;AAAY;;EA2CfA,KAAK,EAAC;AAAe;;EAoBxBA,KAAK,EAAC;AAAoB;;;EAsBMA,KAAK,EAAC;;;EAElCA,KAAK,EAAC;AAAY;;;EAQeA,KAAK,EAAC;;;EAQxCA,KAAK,EAAC;AAAe;;;;;;;;;;;;;;;;;;;;;;uBAhQjCC,mBAAA,CA6QM,OA7QNC,UA6QM,G,4BA5QJC,mBAAA,CAEM;IAFDH,KAAK,EAAC;EAAa,IACtBG,mBAAA,CAAa,YAAT,MAAI,E,qBAGVC,mBAAA,WAAc,EACdD,mBAAA,CA6EM,OA7ENE,UA6EM,GA5EJF,mBAAA,CA4CM,OA5CNG,UA4CM,GA3CJC,YAAA,CAUWC,mBAAA;gBATAC,MAAA,CAAAC,UAAU;+DAAVD,MAAA,CAAAC,UAAU,GAAAC,MAAA;IACnBC,WAAW,EAAC,OAAO;IACnBC,KAAyC,EAAzC;MAAA;MAAA;IAAA,CAAyC;IACzCC,SAAS,EAAT,EAAS;IACRC,OAAK,EAAEN,MAAA,CAAAO;;IAEGC,MAAM,EAAAC,QAAA,CACf,MAA6B,CAA7BX,YAAA,CAA6BY,kBAAA;wBAApB,MAAU,CAAVZ,YAAA,CAAUa,iBAAA,E;;;;gDAIvBb,YAAA,CAcYc,oBAAA;gBAbDZ,MAAA,CAAAa,cAAc;+DAAdb,MAAA,CAAAa,cAAc,GAAAX,MAAA;IACvBC,WAAW,EAAC,MAAM;IAClBC,KAAyC,EAAzC;MAAA;MAAA;IAAA,CAAyC;IACzCC,SAAS,EAAT,EAAS;IACRS,QAAM,EAAEd,MAAA,CAAAe;;sBAET,MAAmC,CAAnCjB,YAAA,CAAmCkB,oBAAA;MAAxBC,KAAK,EAAC,MAAM;MAACC,KAAK,EAAC;2BAC9B1B,mBAAA,CAKE2B,SAAA,QAAAC,WAAA,CAJmBpB,MAAA,CAAAqB,UAAU,EAAtBC,QAAQ;2BADjBC,YAAA,CAKEP,oBAAA;QAHCQ,GAAG,EAAEF,QAAQ,CAACJ,KAAK;QACnBD,KAAK,EAAEK,QAAQ,CAACL,KAAK;QACrBC,KAAK,EAAEI,QAAQ,CAACJ;;;;iDAIrBpB,YAAA,CAcYc,oBAAA;gBAbDZ,MAAA,CAAAyB,cAAc;+DAAdzB,MAAA,CAAAyB,cAAc,GAAAvB,MAAA;IACvBC,WAAW,EAAC,OAAO;IACnBC,KAAqB,EAArB;MAAA;IAAA,CAAqB;IACrBC,SAAS,EAAT,EAAS;IACRS,QAAM,EAAEd,MAAA,CAAAe;;sBAET,MAAoC,CAApCjB,YAAA,CAAoCkB,oBAAA;MAAzBC,KAAK,EAAC,OAAO;MAACC,KAAK,EAAC;2BAC/B1B,mBAAA,CAKE2B,SAAA,QAAAC,WAAA,CAJmBpB,MAAA,CAAA0B,SAAS,EAArBC,QAAQ;2BADjBJ,YAAA,CAKEP,oBAAA;QAHCQ,GAAG,EAAEG,QAAQ;QACbV,KAAK,EAAEU,QAAQ;QACfT,KAAK,EAAES;;;;mDAKdjC,mBAAA,CA6BM,OA7BNkC,UA6BM,GA5BJ9B,YAAA,CAGY+B,oBAAA;IAHDC,IAAI,EAAC,SAAS;IAAEC,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAA9B,MAAA,IAAEF,MAAA,CAAAiC,gBAAgB;;sBAChD,MAA6B,CAA7BnC,YAAA,CAA6BY,kBAAA;wBAApB,MAAU,CAAVZ,YAAA,CAAUoC,iBAAA,E;;qDAAU,QAE/B,G;;;MACApC,YAAA,CAGY+B,oBAAA;IAHDC,IAAI,EAAC,SAAS;IAAEC,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAA9B,MAAA,IAAEF,MAAA,CAAAmC,sBAAsB;;sBACtD,MAAmC,CAAnCrC,YAAA,CAAmCY,kBAAA;wBAA1B,MAAgB,CAAhBZ,YAAA,CAAgBsC,uBAAA,E;;qDAAU,SAErC,G;;;MACAtC,YAAA,CAOY+B,oBAAA;IANVC,IAAI,EAAC,QAAQ;IACZO,QAAQ,GAAGrC,MAAA,CAAAsC,aAAa,IAAItC,MAAA,CAAAsC,aAAa,CAACC,MAAM;IAChDR,OAAK,EAAE/B,MAAA,CAAAwC;;sBAER,MAA6B,CAA7B1C,YAAA,CAA6BY,kBAAA;wBAApB,MAAU,CAAVZ,YAAA,CAAU2C,iBAAA,E;;yBAAU,SACvB,GAAAC,gBAAA,CAAG1C,MAAA,CAAAsC,aAAa,GAAGtC,MAAA,CAAAsC,aAAa,CAACC,MAAM,QAAO,IACtD,gB;;8CACAzC,YAAA,CAOY+B,oBAAA;IANVC,IAAI,EAAC,SAAS;IACbO,QAAQ,GAAGrC,MAAA,CAAAsC,aAAa,IAAItC,MAAA,CAAAsC,aAAa,CAACC,MAAM;IAChDR,OAAK,EAAEY,IAAA,CAAAC;;sBAER,MAA+B,CAA/B9C,YAAA,CAA+BY,kBAAA;wBAAtB,MAAY,CAAZZ,YAAA,CAAY+C,mBAAA,E;;yBAAU,SACzB,GAAAH,gBAAA,CAAG1C,MAAA,CAAAsC,aAAa,GAAGtC,MAAA,CAAAsC,aAAa,CAACC,MAAM,QAAO,IACtD,gB;;8CACAzC,YAAA,CAGY+B,oBAAA;IAHDC,IAAI,EAAC,SAAS;IAAEC,OAAK,EAAE/B,MAAA,CAAA8C;;sBAChC,MAA8B,CAA9BhD,YAAA,CAA8BY,kBAAA;wBAArB,MAAW,CAAXZ,YAAA,CAAWiD,kBAAA,E;;qDAAU,QAEhC,G;;;sCAIJpD,mBAAA,YAAe,EACfD,mBAAA,CA8EM,OA9ENsD,UA8EM,G,+BA7EJzB,YAAA,CA+DW0B,mBAAA;IA9DRC,IAAI,EAAElD,MAAA,CAAAmD,KAAK;IAEXC,iBAAgB,EAAEpD,MAAA,CAAAqD,qBAAqB;IACxCC,MAAM,EAAN,EAAM;IACNlD,KAAmB,EAAnB;MAAA;IAAA;;sBAEA,MAA+C,CAA/CN,YAAA,CAA+CyD,0BAAA;MAA9BzB,IAAI,EAAC,WAAW;MAAC0B,KAAK,EAAC;QAExC1D,YAAA,CAOkByD,0BAAA;MAPDE,IAAI,EAAC,MAAM;MAACxC,KAAK,EAAC,KAAK;MAAC,WAAS,EAAC;;MACtCyC,OAAO,EAAAjD,QAAA,CAIVkD,KAJiB,KACvBjE,mBAAA,CAGM,OAHNkE,UAGM,G,CAFYD,KAAK,CAACE,GAAG,CAACC,MAAM,I,cAAhCvC,YAAA,CAA4Eb,kBAAA;;QAA1CnB,KAAK,EAAC;;0BAAe,MAAW,CAAXO,YAAA,CAAWiE,kBAAA,E;;gEAAU,GAC5E,GAAArB,gBAAA,CAAGiB,KAAK,CAACE,GAAG,CAACG,IAAI,iB;;QAKvBlE,YAAA,CAA2DyD,0BAAA;MAA1CE,IAAI,EAAC,UAAU;MAACxC,KAAK,EAAC,KAAK;MAACuC,KAAK,EAAC;QAEnD1D,YAAA,CAIkByD,0BAAA;MAJDE,IAAI,EAAC,UAAU;MAACxC,KAAK,EAAC,IAAI;MAACuC,KAAK,EAAC;;MACrCE,OAAO,EAAAjD,QAAA,CACwDkD,KADjD,KACvB7D,YAAA,CAAwEmE,iBAAA;QAAhEC,IAAI,EAAC;MAAO;0BAAC,MAA0C,C,kCAAvClE,MAAA,CAAAmE,gBAAgB,CAACR,KAAK,CAACE,GAAG,CAACvC,QAAQ,kB;;;;QAI/DxB,YAAA,CAA6DyD,0BAAA;MAA5CE,IAAI,EAAC,WAAW;MAACxC,KAAK,EAAC,MAAM;MAACuC,KAAK,EAAC;QAErD1D,YAAA,CAIkByD,0BAAA;MAJDE,IAAI,EAAC,MAAM;MAACxC,KAAK,EAAC,MAAM;MAACuC,KAAK,EAAC;;MACnCE,OAAO,EAAAjD,QAAA,CACoBkD,KADb,K,kCACpB3D,MAAA,CAAAoE,cAAc,CAACT,KAAK,CAACE,GAAG,CAACK,IAAI,kB;;QAIpCpE,YAAA,CAA8DyD,0BAAA;MAA7CE,IAAI,EAAC,YAAY;MAACxC,KAAK,EAAC,MAAM;MAACuC,KAAK,EAAC;QAEtD1D,YAAA,CAMkByD,0BAAA;MANDtC,KAAK,EAAC,IAAI;MAACuC,KAAK,EAAC;;MACrBE,OAAO,EAAAjD,QAAA,CAGPkD,KAHc,KACvB7D,YAAA,CAESmE,iBAAA;QAFAnC,IAAI,EAAE6B,KAAK,CAACE,GAAG,CAACC,MAAM;QAAyBI,IAAI,EAAC;;0BAC3D,MAAoC,C,kCAAjCP,KAAK,CAACE,GAAG,CAACC,MAAM,+B;;;;QAKzBhE,YAAA,CAkBkByD,0BAAA;MAlBDtC,KAAK,EAAC,IAAI;MAACuC,KAAK,EAAC,KAAK;MAACa,KAAK,EAAC;;MACjCX,OAAO,EAAAjD,QAAA,CAQJkD,KARW,KACvB7D,YAAA,CAOY+B,oBAAA;QANVC,IAAI,EAAC,SAAS;QACdoC,IAAI,EAAC,OAAO;QACX7B,QAAQ,GAAGsB,KAAK,CAACE,GAAG,CAACC,MAAM;QAC3B/B,OAAK,EAAA7B,MAAA,IAAEF,MAAA,CAAAsE,cAAc,CAACX,KAAK,CAACE,GAAG;;0BACjC,MAED7B,MAAA,SAAAA,MAAA,Q,iBAFC,MAED,E;;;oEACAlC,YAAA,CAMY+B,oBAAA;QALVC,IAAI,EAAC,QAAQ;QACboC,IAAI,EAAC,OAAO;QACXnC,OAAK,EAAA7B,MAAA,IAAEF,MAAA,CAAAuE,YAAY,CAACZ,KAAK,CAACE,GAAG;;0BAC/B,MAED7B,MAAA,SAAAA,MAAA,Q,iBAFC,MAED,E;;;;;;;2EA1DOhC,MAAA,CAAAwE,OAAO,E,GA+DpB7E,mBAAA,QAAW,EACXD,mBAAA,CAUM,OAVN+E,UAUM,GATJ3E,YAAA,CAQE4E,wBAAA;IAPQ,cAAY,EAAE1E,MAAA,CAAA2E,WAAW;gEAAX3E,MAAA,CAAA2E,WAAW,GAAAzE,MAAA;IACzB,WAAS,EAAEF,MAAA,CAAA4E,QAAQ;6DAAR5E,MAAA,CAAA4E,QAAQ,GAAA1E,MAAA;IAC1B,YAAU,EAAE,iBAAiB;IAC7B2E,KAAK,EAAE7E,MAAA,CAAA6E,KAAK;IACbC,MAAM,EAAC,yCAAyC;IAC/CC,YAAW,EAAE/E,MAAA,CAAAe,SAAS;IACtBiE,eAAc,EAAEhF,MAAA,CAAAe;0GAKvBpB,mBAAA,aAAgB,EAChBG,YAAA,CAwCYmF,oBAAA;gBAvCDjF,MAAA,CAAAiC,gBAAgB;+DAAhBjC,MAAA,CAAAiC,gBAAgB,GAAA/B,MAAA;IACzBgF,KAAK,EAAC,MAAM;IACZ1B,KAAK,EAAC,OAAO;IACZ2B,OAAK,EAAEnF,MAAA,CAAAoF;;IAwBGC,MAAM,EAAA5E,QAAA,CACf,MASO,CATPf,mBAAA,CASO,QATP4F,UASO,GARLxF,YAAA,CAA2D+B,oBAAA;MAA/CE,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAA9B,MAAA,IAAEF,MAAA,CAAAiC,gBAAgB;;wBAAU,MAAED,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;QAC/ClC,YAAA,CAMY+B,oBAAA;MALVC,IAAI,EAAC,SAAS;MACb0C,OAAO,EAAExE,MAAA,CAAAuF,SAAS;MAClBxD,OAAK,EAAE/B,MAAA,CAAAwF;;wBAER,MAAmC,C,kCAAhCxF,MAAA,CAAAuF,SAAS,qC;;;sBA9BlB,MAoBY,CApBZzF,YAAA,CAoBY2F,oBAAA;MAnBVC,GAAG,EAAC,WAAW;MACdC,MAAM,EAAE3F,MAAA,CAAA4F,SAAS;MACjB,aAAW,EAAE,KAAK;MAClB,WAAS,EAAE5F,MAAA,CAAA6F,gBAAgB;MAC3B,YAAU,EAAE7F,MAAA,CAAA8F,mBAAmB;MAC/B,UAAQ,EAAE9F,MAAA,CAAA+F,iBAAiB;MAC3B,WAAS,EAAE/F,MAAA,CAAAgG,QAAQ;MACpBC,IAAI,EAAJ,EAAI;MACJC,QAAQ,EAAR;;MAMWC,GAAG,EAAA1F,QAAA,CACZ,MAEMuB,MAAA,SAAAA,MAAA,QAFNtC,mBAAA,CAEM;QAFDH,KAAK,EAAC;MAAgB,GAAC,0BAE5B,mB;wBAPF,MAA4D,CAA5DO,YAAA,CAA4DY,kBAAA;QAAnDnB,KAAK,EAAC;MAAiB;0BAAC,MAAiB,CAAjBO,YAAA,CAAiBsG,wBAAA,E;;sCAClD1G,mBAAA,CAEM;QAFDH,KAAK,EAAC;MAAiB,I,iBAAC,YAClB,GAAAG,mBAAA,CAAa,YAAT,MAAI,E;;;;;gDAuBvBC,mBAAA,cAAiB,EACjBG,YAAA,CA0DYmF,oBAAA;gBAzDDjF,MAAA,CAAAmC,sBAAsB;iEAAtBnC,MAAA,CAAAmC,sBAAsB,GAAAjC,MAAA;IAC/BgF,KAAK,EAAC,OAAO;IACb1B,KAAK,EAAC,OAAO;IACZ2B,OAAK,EAAEnF,MAAA,CAAAqG;;IAyCGhB,MAAM,EAAA5E,QAAA,CACf,MAUO,CAVPf,mBAAA,CAUO,QAVP4G,WAUO,GATLxG,YAAA,CAAiE+B,oBAAA;MAArDE,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAA9B,MAAA,IAAEF,MAAA,CAAAmC,sBAAsB;;wBAAU,MAAEH,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;QACrDlC,YAAA,CAOY+B,oBAAA;MANVC,IAAI,EAAC,SAAS;MACb0C,OAAO,EAAExE,MAAA,CAAAuF,SAAS;MAClBlD,QAAQ,EAAErC,MAAA,CAAAuG,WAAW,CAAChE,MAAM;MAC5BR,OAAK,EAAE/B,MAAA,CAAAwG;;wBAER,MAA+D,C,kCAA5DxG,MAAA,CAAAuF,SAAS,uBAAuBvF,MAAA,CAAAuG,WAAW,CAAChE,MAAM,wB;;;sBAhD3D,MAqCM,CArCN7C,mBAAA,CAqCM,OArCN+G,UAqCM,GApCJ/G,mBAAA,CAOE;MANAgG,GAAG,EAAC,gBAAgB;MACpB5D,IAAI,EAAC,MAAM;MACX4E,eAAe,EAAf,EAAe;MACfR,QAAQ,EAAR,EAAQ;MACR9F,KAAqB,EAArB;QAAA;MAAA,CAAqB;MACpBU,QAAM,EAAAkB,MAAA,QAAAA,MAAA,UAAA2E,IAAA,KAAE3G,MAAA,CAAA4G,kBAAA,IAAA5G,MAAA,CAAA4G,kBAAA,IAAAD,IAAA,CAAkB;oDAE7BjH,mBAAA,CAWM;MAVJH,KAAK,EAAC,kBAAkB;MACvBwC,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAA2E,IAAA,KAAE3G,MAAA,CAAA6G,YAAA,IAAA7G,MAAA,CAAA6G,YAAA,IAAAF,IAAA,CAAY;QAEpB7G,YAAA,CAAuDY,kBAAA;MAA9CnB,KAAK,EAAC;IAAa;wBAAC,MAAgB,CAAhBO,YAAA,CAAgBsC,uBAAA,E;;oCAC7C1C,mBAAA,CAEM;MAFDH,KAAK,EAAC;IAAoB,GAAC,WAEhC,qB,4BACAG,mBAAA,CAEM;MAFDH,KAAK,EAAC;IAAmB,GAAC,gBAE/B,oB,GAGSS,MAAA,CAAAuG,WAAW,CAAChE,MAAM,Q,cAA7B/C,mBAAA,CAcM,OAdNsH,WAcM,GAbJpH,mBAAA,CAA8C,YAA1C,WAAS,GAAAgD,gBAAA,CAAG1C,MAAA,CAAAuG,WAAW,CAAChE,MAAM,IAAG,MAAI,iBACzC7C,mBAAA,CAWM,OAXNqH,WAWM,I,kBAVJvH,mBAAA,CAMM2B,SAAA,QAAAC,WAAA,CALWpB,MAAA,CAAAuG,WAAW,CAACS,KAAK,SAAzBC,IAAI;2BADbzH,mBAAA,CAMM;QAJHgC,GAAG,EAAEyF,IAAI,CAACjD,IAAI;QACfzE,KAAK,EAAC;0BAEH0H,IAAI,CAACC,kBAAkB,IAAID,IAAI,CAACjD,IAAI;oCAE9BhE,MAAA,CAAAuG,WAAW,CAAChE,MAAM,S,cAA7B/C,mBAAA,CAEM,OAFN2H,WAEM,EAFiD,UAC9C,GAAAzE,gBAAA,CAAG1C,MAAA,CAAAuG,WAAW,CAAChE,MAAM,SAAQ,OACtC,mB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}